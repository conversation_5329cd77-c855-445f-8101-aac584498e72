
import router from '@ohos.router';
import App from '@system.app'
import prompt from '@ohos.promptAction';

@Entry
@Component
struct yishangpinPage {
  @State isCollected: boolean = false; // 收藏状态
  @State showToast: boolean = false; // 控制提示显示
  @State toastMessage: string = ''; // 提示消息内容
  @State showBuyDialog: boolean = false; // 控制购买弹窗显示
  @State quantity: number = 3; // 购买数量
  @State isAddToCart: boolean = false; // 是否是加入购物车操作
  
  // 切换收藏状态
  toggleCollection() {
    this.isCollected = !this.isCollected;
    this.toastMessage = this.isCollected ? '收藏成功' : '取消收藏';
    this.showToast = true;
    
    // 2秒后自动隐藏提示
    setTimeout(() => {
      this.showToast = false;
    }, 2000);
  }
  
  // 显示购买弹窗
  showBuyOptions(isAddToCart: boolean) {
    this.isAddToCart = isAddToCart;
    this.showBuyDialog = true;
  }
  
  // 增加数量
  increaseQuantity() {
    this.quantity++;
  }
  
  // 减少数量
  decreaseQuantity() {
    if (this.quantity > 1) {
      this.quantity--;
    }
  }
  
  // 确认购买
  confirmPurchase() {
    if (this.isAddToCart) {
      this.toastMessage = `已将${this.quantity}kg苹果加入购物车`;
    } else {
      this.toastMessage = `正在购买${this.quantity}kg苹果`;
    }
    this.showToast = true;
    this.showBuyDialog = false;
    
    // 2秒后自动隐藏提示
    setTimeout(() => {
      this.showToast = false;
    }, 2000);
  }
  
  build() {
    Stack() {
      Column() {
        //滑动的整个页面
        Scroll() {
          Column({ space: 10 }) {
            //上面图片
            Stack({ alignContent: Alignment.TopStart }) {
              Swiper() {
                Image($r('app.media.apple5'))
                Image($r('app.media.apple3'))
                Image($r('app.media.apple1'))
                Image($r('app.media.apple6'))
              }
              .autoPlay(true)
              .width('100%')
              .height(250)
              .borderRadius(15)

              Image($r('app.media.fanhui1'))
                .width(25)
                .height(25)
                .onClick(() => {
                  router.back()
                })
                /*.onClick(() => {
                  router.pushUrl({
                    url: 'pages/applePage'
                  })
                })*/
            }.margin({top:30})
            .width('100%')
            .height(250)

            //价钱名字等
            Column({ space: 10 }) {
              Row({ space: 15 }) {
                Text('￥60.00 ')
                  .fontColor('#ffec0606')
                  .fontSize(30)
                  .fontWeight(80)
                  .margin({ left: 10 })
                Text('/1kg')
                  .fontColor('#ffd0cece')
                  .fontSize(15)
                  .fontWeight(30)
                Text('已售60kg')
                  .fontColor('#ffd0cece')
                  .fontSize(15)
                  .fontWeight(30)
                  .margin({ left: 120 })

              }
              .width('100%')
              .height(40)

              Row({ space: 60 }) {
                Text('纯天然大大大大大大大大苹果')
                  .fontColor('#000000')
                  .fontSize(21)
                  .fontWeight(80)
                  .margin({ left: 10 })
                
                // 收藏按钮 - 根据状态显示不同颜色
                Image(this.isCollected ? $r('app.media.aixinred') : $r('app.media.aixin'))
                  .width(25)
                  .height(25)
                  .fillColor(this.isCollected ? '#ff0000' : '#000000')
                  .onClick(() => {
                    this.toggleCollection()
                  })
              }
              .width('100%')
              .height(40)

            }
            .backgroundColor('#ffffff')
            .height(100)
            .borderColor('#ff66b536')
            .borderWidth(1)

            //快递部分
            Column() {
              //配送
              Row() {
                Text('配送')
                  .fontColor('#BCBCBC')
                  .fontSize(18)
                  .fontWeight(60)
                  .margin({ left: 5 })

                Text('发货至 河北省-石家庄市')
                  .fontColor('#000000')
                  .fontSize(18)
                  .fontWeight(60)
                  .margin({ left: 25 })

                Image($r('app.media.jinru'))
                  .width(18)
                  .height(18)
                  .margin(90)
                  .onClick(() => {
                  router.pushUrl({
                    url: 'pages/Myaddress'
                  })
                })


              }
              .height(45)

              //运费
              Row() {
                Text('运费')
                  .fontColor('#BCBCBC')
                  .fontSize(18)
                  .fontWeight(60)
                  .margin({ left: 5 })

                Text('顺丰快递10元起')
                  .fontColor('#000000')
                  .fontSize(18)
                  .fontWeight(60)
                  .margin({ left: 25 })


              }
              .height(45)
              .width('100%')

              //放心购买
              Row({ space: 20 }) {
                Text('品质保证')
                  .fontColor('#BCBCBC')
                  .fontSize(16)
                  .fontWeight(60)
                  .margin(10)

                Text('放心购买')
                  .fontColor('#BCBCBC')
                  .fontSize(16)
                  .fontWeight(60)

                Text('交易安全')
                  .fontColor('#BCBCBC')
                  .fontSize(16)
                  .fontWeight(60)

                Text('平台担保')
                  .fontColor('#BCBCBC')
                  .fontSize(16)
                  .fontWeight(60)

              }
              .height(40)
              .width('100%')

            }
            .width('100%')
            .backgroundColor('#ffffff')
            .height(140)
            .borderColor('#ff66b536')
            .borderWidth(1)

            //用户评价部分
            Column() {
              //上排评价数量
              Row() {
                Text('用户评价')
                  .fontColor('#000000')
                  .fontSize(22)
                  .fontWeight(60)
                  .margin({ left: 5 })

                Text('（678）')
                  .fontColor('#000000')
                  .fontSize(22)
                  .fontWeight(60)
                  .margin({ left: 12 })

                Image($r('app.media.jinru'))
                  .width(18)
                  .height(18)
                  .margin({ left: 160 })
                  .onClick(() => {
                    router.pushUrl({
                      url: 'pages/pingjiaPage'
                    })
                  })
              }
              .width('100%')
              .height(40)

              //下面一个评论
              Row() {
                Image($r('app.media.apple2'))
                  .width(80)
                  .height(80)
                  .margin(10)
                  .borderRadius(12)

                Column({ space: 5 }) {
                  Text('水果确实很新鲜，验证了，确实是有机的，快递速度也非常快，非常快，非常快的…')
                    .fontColor('#BCBCBC')
                    .fontSize(16)
                    .fontWeight(60)
                    .width('100%')

                  Row({ space: 5 }) {
                    Image($r('app.media.a'))
                      .width(20)
                      .height(20)
                      .borderRadius(18)

                    Text('gkkkkkkw')
                      .fontColor('#BCBCBC')
                      .fontSize(16)
                      .fontWeight(60)
                  }
                  .width(270)
                  .padding({ left: 5 })
                }
                .padding(5)
                .width(270)

              }
              .width('100%')

            }
            .width('100%')
            .backgroundColor('#ffffff')
            .height(150)
            .borderColor('#ff66b536')
            .borderWidth(1)

            //商品详情部分
            Column({space:10}) {

              Text('商品详情')
                .fontColor('#000000')
                .fontSize(20)
                .fontWeight(70)
                .margin({ left: 5 })

              Text('清爽的青苹果，爽口，咬下去好像带着一点点刺一样，麻麻的，带着稍微刺激的酸酸的味道就好象我们无知的单纯的意识，活力而自信。成熟的苹果看起来鲜艳通红，你轻轻咬开它，新鲜、香甜的滋味就会让蔓延开来，你的嘴唇上、舌头上同时染满了汁水。苹果的果肉就是个白胖胖的小孩，没有熟透的苹果颜色是青色的，咬在嘴里酸酸的还有点涩味。')
                .fontColor('#000000')
                .fontSize(18)
                .fontWeight(30)
                .padding(10)

              Image($r('app.media.apple7'))
                .width(380)
                .height(350)
                .padding(10)

              Text('清爽的青苹果，爽口，咬下去好像带着一点点刺一样，麻麻的，带着稍微刺激的酸酸的味道就好象我们无知的单纯的意识，活力而自信。成熟的苹果看起来鲜艳通红，你轻轻咬开它，新鲜、香甜的滋味就会让蔓延开来，你的嘴唇上、舌头上同时染满了汁水。苹果的果肉就是个白胖胖的小孩，没有熟透的苹果颜色是青色的，咬在嘴里酸酸的还有点涩味。')
                .fontColor('#000000')
                .fontSize(18)
                .fontWeight(30)
                .padding(10)

              Image($r('app.media.apple8'))
                .width(380)
                .height(350)
                .padding(10)


            }
            .width('100%')
            .backgroundColor('#ffffff')
            .height(800)
            .borderColor('#ff66b536')
            .borderWidth(1)

          }
          .backgroundColor('#F4F8FF')
          .width('100%')
          .height('auto')
        }
        .scrollBar(BarState.Off)
        .scrollable(ScrollDirection.Vertical)
        .height('90%')

        //底部四个按钮
        Row({space:15}){
          Column({space:5}){
            Image($r('app.media.lianxikefu'))
              .width(25)
              .height(25)
            Text('联系客服')
              .fontColor('#BCBCBC')
              .fontSize(16)
              .fontWeight(60)
          }.onClick(() => {
            router.pushUrl({
              url: 'pages/zzzcallkefuPage'
            })
          })

          Column({space:5}){
            Image($r('app.media.gouwuche'))
              .width(25)
              .height(25)
            Text('购物车')
              .fontColor('#BCBCBC')
              .fontSize(16)
              .fontWeight(60)
          }.onClick(() => {
            router.pushUrl({
              url: 'pages/ShoppingCart'
            })

          })
          Text('加入购物车')
            .height(40)
            .width(110)
            .fontColor('#ffffff')
            .fontSize(18)
            .backgroundColor('#FF9900')
            .borderRadius(5)
            .padding(10)
            .onClick(() => {
              this.showBuyOptions(true)
            })

          Text('立即购买')
            .height(40)
            .width(110)
            .fontColor('#ffffff')
            .fontSize(18)
            .backgroundColor('#99CC33')
            .borderRadius(5)
            .padding(10)
            .onClick(() => {
              this.showBuyOptions(false)
            })
        }
        .width('100%')
      }
      .width('100%')
      .height('100%')
      
      // 底部提示框
      if (this.showToast) {
        Column() {
          Text(this.toastMessage)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor('#000000')
            .opacity(0.7)
            .borderRadius(20)
            .padding({left: 20, right: 20, top: 10, bottom: 10})
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.End)
        .alignItems(HorizontalAlign.Center)
        .margin({bottom: 100})
      }
      
      // 购买数量选择弹窗
      if (this.showBuyDialog) {
        Column() {
          // 半透明背景
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('#000000')
            .opacity(0.5)
            .onClick(() => {
              this.showBuyDialog = false
            })
        }
        .width('100%')
        .height('100%')
        .position({ x: 0, y: 0 })
        
        // 弹窗内容
        Column() {
          // 关闭按钮
          Row() {
            Blank()
            Image($r('app.media.chacha'))
              .width(24)
              .height(24)
              .margin({ right: 16, top: 16 })
              .onClick(() => {
                this.showBuyDialog = false
              })
          }
          .width('100%')
          
          // 商品信息
          Row({ space: 15 }) {
            Image($r('app.media.apple1'))
              .width(80)
              .height(80)
              .borderRadius(8)
              .margin({ left: 16 })
            
            Column({ space: 8 }) {
              Text('￥60.00')
                .fontSize(20)
                .fontColor('#FF0000')
                .fontWeight(FontWeight.Bold)
              
              Text('/1kg')
                .fontSize(14)
                .fontColor('#999999')
              
              Text('请选择数量（单位：kg）')
                .fontSize(14)
                .fontColor('#333333')
                .margin({ top: 8 })
            }
            .alignItems(HorizontalAlign.Start)
            .margin({ right: 16 })
          }
          .width('100%')
          .margin({ top: 16 })
          
          // 数量选择
          Row() {
            Button('-')
              .width(40)
              .height(40)
              .fontSize(20)
              .fontWeight(FontWeight.Bold)
              .backgroundColor('#F5F5F5')
              .fontColor('#333333')
              .borderRadius(4)
              .onClick(() => {
                this.decreaseQuantity()
              })
            
            Text(this.quantity.toString())
              .width(60)
              .height(40)
              .fontSize(18)
              .textAlign(TextAlign.Center)
              .border({ width: 1, color: '#EEEEEE' })
              .borderRadius(4)
              .margin({ left: 8, right: 8 })
            
            Button('+')
              .width(40)
              .height(40)
              .fontSize(20)
              .fontWeight(FontWeight.Bold)
              .backgroundColor('#F5F5F5')
              .fontColor('#333333')
              .borderRadius(4)
              .onClick(() => {
                this.increaseQuantity()
              })
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          .margin({ top: 24, bottom: 24 })
          
          // 确定按钮
          Button('确定')
            .width('90%')
            .height(50)
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .backgroundColor('#99CC33')
            .borderRadius(25)
            .margin({ bottom: 16 })
            .onClick(() => {
              this.confirmPurchase()

                  router.pushUrl({
                    url: 'pages/settle'
                  })

            }).onClick(() => {
            this.confirmPurchase()

            router.pushUrl({
              url: 'pages/settle'
            })

          })
        }
        .width('100%')
        .position({ x: 0, y: '60%' })
        .backgroundColor(Color.White)
        .borderRadius({ topLeft: 16, topRight: 16 })
      }
    }
    .width('100%')
    .height('100%')
  }
}
