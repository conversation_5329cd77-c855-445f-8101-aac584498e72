import { router, Router } from "@kit.ArkUI";

@Component

export struct home{

  @State message: string = 'Hello World';
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;
  @State isLoading:boolean=true
  private jumpTofenlei!:()=>void;




  build() {
    Column(){
      Stack({alignContent:Alignment.TopStart}){
        Image($r('app.media.lv'))
          .width('100%')
          .height('14%')

      Column(){
          Row({space:5}){
            Search({placeholder:'精品水果'})
              .backgroundColor('#FFFFFF')
              .width('60%')
              .margin({left:10})

            Row({
              space: 10,
            }) {
              Image($r('app.media.pot'))
                .width(24)
                .fillColor('#ffffff');

              Divider()
                .vertical(true)
                .strokeWidth(1)
                .color('#ffffff')
                .height(22)

              Image($r('app.media.exitcircle'))
                .width(24)
                .fillColor('#ffffff');
            }
            .border({
              width: 1,
              radius:20,
              color: '#6B8F24' // 淡黑色边框
            })
            .padding({ left: 20, right: 20,top: 8,bottom:8 })
            .backgroundColor('#6B8F24') // 淡黑色背景
            .margin({left:22, right: 10, top: 4 ,bottom:7}); // 调整右边距和顶边距

          }

          List() {
            ListItem() {
              Column() {
                Swiper() {
                  Image($r('app.media.u1721'))
                  Image($r('app.media.u1722'))
                  Image($r('app.media.u1721'))

                }
                .autoPlay(true)
                .width('100%')


                Row() {
                  Column({ space: 10 }) {
                    //图标
                    Image($r('app.media.u1726'))
                      .width(60)
                      .height(60)
                    //文本
                    Text('推荐')
                      .fontSize(14)
                  }

                  Column({ space: 10 }) {
                    //图标
                    Image($r('app.media.u1729'))
                      .width(60)
                      .height(60)
                    //文本
                    Text('水果类')
                      .fontSize(14)
                  }

                  Column({ space: 10 }) {
                    //图标
                    Image($r('app.media.u1732'))
                      .width(60)
                      .height(60)
                    //文本
                    Text('蔬菜类')
                      .fontSize(14)
                  }

                  Column({ space: 10 }) {
                    //图标
                    Image($r('app.media.u1735'))
                      .width(60)
                      .height(60)
                    //文本
                    Text('干果类')
                      .fontSize(14)
                  }

                  Column({ space: 10 }) {
                    //图标
                    Image($r('app.media.u1738'))
                      .width(60)
                      .height(60)
                    //文本
                    Text('粮油类')
                      .fontSize(14)
                  }
                }.onClick(() => {
                  this.jumpTofenlei();
                })

                .margin({ top: 15 })
                .width('100%')
                .justifyContent(FlexAlign.SpaceAround)

                Stack() {
                  Image($r('app.media.dibu'))
                    .borderRadius(12)
                    .height(80)
                    .width('100%')
                  Row() {
                    Column() {
                      Text('有奖邀请活动火热进行中...').fontColor('#FFFFFF').fontSize('20')
                      Text('活动日期:7.6-7.30').fontColor('#F0B8B7').margin({ top: 5 }).fontSize('15')
                    }.alignItems(HorizontalAlign.Start)
                    .margin({left:10})

                    Image($r('app.media.hezi'))
                      .height(70)
                      .width(70)
                      .margin({ left: 30 })
                  }
                  .justifyContent(FlexAlign.Start)
                  .width('100%')
                }
                .margin({ top: 15 })

                Image($r('app.media.zuixin'))
                  .margin({ top: 15 })
                  .width('100%')
                  .height(35)

                Column() {
                  Row({ space: 10 }) {
                    Column({ space: 10 }) {
                      Image($r('app.media.lp'))
                        .width('45%')
                        .height(100)
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥60.00')
                        .fontColor('#FF0070')
                    }
                    .onClick(()=>{
                      router.replaceUrl({
                        url:'pages/Starter'
                      })
                    })
                    .alignItems(HorizontalAlign.Start)

                    Column({ space: 10 }) {
                      Image($r('app.media.lp'))
                        .width('45%')
                        .height(100)
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥60.00')
                        .fontColor('#FF0070')
                    }
                    .alignItems(HorizontalAlign.Start)
                  }

                  Row({ space: 10 }) {
                    Column({ space: 10 }) {
                      Image($r('app.media.lp'))
                        .width('45%')
                        .height(100)
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥60.00')
                        .fontColor('#FF0070')
                    }
                    .alignItems(HorizontalAlign.Start)

                    Column({ space: 10 }) {
                      Image($r('app.media.lp'))
                        .width('45%')
                        .height(100)
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥60.00')
                        .fontColor('#FF0070')
                    }
                    .alignItems(HorizontalAlign.Start)
                  }

                }.onClick(() => {
                  router.pushUrl({
                    url: 'pages/yishangpinPage'
                  })
                })


                Image($r('app.media.remai'))
                  .margin({ top: 15 })
                  .width('100%')
                  .height(35)
                  .margin({left:30,right:30})

                Column({ space: 10 }) {
                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')


                }.onClick(() => {
                  router.pushUrl({
                    url: 'pages/yishangpinPage'
                  })
                })


                Image($r('app.media.gengduo'))
                  .margin({ top: 15 })
                  .width('100%')
                  .height(35)
                  .margin({left:30,right:30})

                Column({ space: 10 }) {
                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                  Row({ space: 10 }) {
                    Image($r('app.media.hping'))
                      .width(100)
                      .height(100)
                      .borderRadius(8)
                      .margin({left:20})

                    Column({ space: 10 }) {
                      Text('纯天然水果（500g）')
                      Text('纯天然种植 | 自然生长 | 绿色食品')
                        .fontSize(10)
                        .fontColor('#B4A4B9')
                      Text('¥20.00')
                        .fontColor('#FF0070')

                    }.alignItems(HorizontalAlign.Start)
                  }.justifyContent(FlexAlign.Start)
                  .width('100%')

                }.onClick(() => {
                  router.pushUrl({
                    url: 'pages/yishangpinPage'
                  })
                })


              }
            }

            if(this.isLoading){
              ListItem(){
                Row(){
                  Text('松开加载更多...')
                  LoadingProgress()
                    .width(20)
                    .height(20)
                }
                .padding(40)
                .width('100%')
                .justifyContent(FlexAlign.Center)
              }
            }

          }


          .backgroundColor('#FAFFEF')
           .scrollBar(BarState.Off)




      } .padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
      .width('100%')
        .height('100%')



      }


    }
    .width('100%')
    .height('100%')


  }

}

