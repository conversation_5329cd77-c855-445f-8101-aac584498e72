import {  router } from "@kit.ArkUI"


@Entry
@Component
struct NewAddress {
  @State message: string = 'Hello World';
  @State name: string = '';
  @State phone: string = '';
  @State region: string = '';
  @State detailAddress: string = ''
  @State isDefault: boolean = false

  build() {
    Column(){
      Row() {
        Image($r("app.media.return"))
          .width(20)
          .margin({ left: 10, top: 5 })
          .fillColor('#ffffff')
          .onClick(() => {
            router.back()
          })
        Text('添加新地址')
          .fontColor('#ffffff')
          .margin({ left: 80, top: 5 })
          .fontSize(18)
          .fontWeight(88)
        Row({
          space: 10
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({
          left: 20,
          right: 20,
          top: 8,
          bottom: 8
        })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({ left: 22, right: 10, top: 5 });
      }
      .justifyContent(FlexAlign.SpaceBetween)
      .width('100%')
      .padding({ left: 5, right: 5 })
      .height(60)
      .backgroundColor('#99CC33')

      Row() {
        Column({ space: 5 }) {
          Row({space:5}){
            Text('姓名')
            TextInput({ placeholder: '收货人姓名' })
              .backgroundColor('#ffffff')
              .onChange((value) => this.name = value)
          }

          Row({space:5}){
            Text('电话')
            TextInput({ placeholder: '收货人电话' })
              .backgroundColor('#ffffff')
              .onChange((value) => this.phone = value)
          }
        }
        .width(250)
        .padding(10)
        .backgroundColor('#fff');

        Image($r('app.media.wode')) // 用户图标
          .backgroundColor('#ffffff')
          .width(80)
          .height(100)
          .padding({left:15,right:15,top:25,bottom:25})


      }
      .width('100%')
      .height(100)
      .margin(10)
      .borderRadius(10)

      // 所在地区选择器
      TextInput({ placeholder: '所在地区（省、市、区）' })
        .onChange((value) => this.region = value)
        .width('100%')
        .padding(10)
        .borderRadius(10)
        .backgroundColor('#fff');

      // 详细地址输入框
      TextInput({ placeholder: '详细地址(街道、门牌)' })
        .onChange((value) => this.detailAddress = value)
        .width('100%')
        .padding(10)
        .margin({left:10,right:10,top:1})
        .borderRadius(10)
        .backgroundColor('#fff');

      // 设为默认地址开关
      Row({ space: 10 }) {
        Text('设为默认地址')
          .fontSize(14);
        if(this.isDefault==false){
          Image($r('app.media.switch_close'))
            .height(20)
            .width(40)
            .onClick(()=>{
              this.isDefault= true
            })
        }else{
          Image($r('app.media.switch_open'))
            .height(20)
            .width(40)
            .onClick(()=>{
              this.isDefault=false
            })
        }
      }
      .width('100%')
      .backgroundColor('#ffffff')
      .height(60)
      .justifyContent(FlexAlign.SpaceBetween)
      .padding(10)
      .margin(10)
      .borderRadius(10)

      // 保存按钮
      Button('保存')
        .onClick(() => {
          this.onSave();
        })
        .width('100%')
        .height(40)
        .borderRadius(5)
        .backgroundColor('#99CC33')
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .padding(20)
    .backgroundColor('#F4F8FF');
  }

  onSave() {
    console.log('保存地址信息：', {
      name: this.name,
      phone: this.phone,
      region: this.region,
      detailAddress: this.detailAddress,
      isDefault: this.isDefault
    });
  }
}