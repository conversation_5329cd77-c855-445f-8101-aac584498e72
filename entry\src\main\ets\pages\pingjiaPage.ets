import router from '@ohos.router';
interface  Item3{
  img1:Resource
  img2:Resource
  img3:Resource
  touxiang:Resource
  name:string
  time:string
  say:string
}


@Entry
@Component
struct pingjiaPage {

  datas: Item3[] = [

    { img1: $r('app.media.apple1'), img2: $r('app.media.apple2'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkkkkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple4'), img2: $r('app.media.apple2'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkkkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple6'), img2: $r('app.media.apple2'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple7'), img2: $r('app.media.apple2'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple2'), img2: $r('app.media.apple5'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple8'), img2: $r('app.media.apple2'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple1'), img2: $r('app.media.apple4'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
    { img1: $r('app.media.apple3'), img2: $r('app.media.apple2'), img3: $r('app.media.apple4'), touxiang: $r('app.media.a'), name: 'gkkkkw', say: '好吃的很好吃的红苹果', time: '2025.7.18', },
  ]

  //UI
  @Builder
  myItemUI(item: Item3) {

    Column({ space: 10 }) {
      Row(){
        Row() {
          Image(item.touxiang)
            .width(30)
            .height(30)
            .borderRadius(12)
            //.margin(10)

          Text(item.name)
            .fontSize(20)
            .fontColor('#000000')
            .margin({ left: 5 })
        }
        .margin(10)


        Text(item.time)
          .fontSize(20)
          .fontColor('#000000')
          //.margin({right:10})

      }
      .justifyContent(FlexAlign.SpaceBetween)
      .margin({top:5})
      .width('100%')
      .height(32)

      Text(item.say)
        .width('100%')
        .height(32)
        .fontSize(21)
        .fontColor('#000000')
        .margin({left:40})

      Row({space:15}) {
        Image(item.img1)
          .width(100)
          .height(100)

        Image(item.img2)
          .width(100)
          .height(100)

        Image(item.img3)
          .width(100)
          .height(100)
      }
      .margin(10)

    }
    .width(350)
    .height(230)
    .borderRadius(12)
    .backgroundColor('#ffffff')
    .margin({ top: 15 })
  }

  build() {
    //最外层
    Column(){
      //顶部栏目
      Row() {

        //返回键
        Image($r('app.media.fanhui1'))

          .width(20)
          .height(20)
          .onClick(() => {
            router.back()
          })
          /*.onClick(() => {
            router.pushUrl({
              url: 'pages/yishangpinPage'
            })
          })*/

        Text('用户评价')
          .fontSize(25)
          .fontColor('#ffffff')
          .margin({left:80});

        //.margin({right:100})
        //分享和回主页
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:55}); // 调整右边距和顶边距

      }
      .backgroundColor('#99CC33')
      .width('100%')
      .height(80)
      .borderRadius(8)
      .padding(10)

      //下面的评论(可滚动)
      Scroll(){

        Column({space:5}){

          //循环遍历数据渲染列表
          //参数1 表示数据  参数2 遍历的结果
          ForEach(this.datas,(item:Item3)=>{
            this.myItemUI(item)

          })

        }
        .margin({left:8})



      }.scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)

    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F4F8FF')
  }
}