import router from '@ohos.router';

@Entry
@Component
export struct ShoppingCart {
  @State count: number =1
  @State price: number=60
  @State edit: number=1
  @State is_sele :boolean=true
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  build() {
    Column() {
      Row() {
        if(this.edit==1) {
          Text('编辑')
            .fontColor('#ffffff')
            .fontSize(18)
            .margin({left:10,top:5})
            .onClick(() => {
              this.edit = 2
            })
        }else{
          Text('完成')
            .fontColor('#ffffff')
            .fontSize(18)
            .margin({left:10,top:5})
            .onClick(() => {
              this.edit = 1
            })
        }
        Text('购物车')
          .fontColor('#ffffff')
          .fontWeight(88)
          .fontSize(22)
          .margin({left:66,top:5})
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:22, right: 10, top: 5 }); // 调整右边距和顶边距
      }
      .justifyContent(FlexAlign.SpaceBetween)
      .width('100%')
      .padding({left: 5 , right: 5})
      .height(60)
      .backgroundColor('#99CC33')

      Row(){
        //左侧列
        Column({space: 12}) {
          Text('湖北省荆州市学苑路汉科10086栋')
            .fontSize(20)
            .fontWeight(700)
            .margin({top:20,left:10})
          Text('xun先生 153****1234')
            .fontSize(15)
            .fontWeight(700)
            .fontColor('#999')
            .margin({bottom:20,left:10})
        }
        .alignItems(HorizontalAlign.Start)
        Row(){
          Image($r("app.media.right_arrow"))
            .width(20)
            .fillColor('#999')
            .margin({left:10})
        }
      }
      .onClick(()=>{
        router.replaceUrl({url:'pages/myaddress'})
      })
      .justifyContent(FlexAlign.Start)
      .width('100%')
      .height(100)
      .backgroundColor('#fff')
      //商品列表
      Row() {
        if(this.is_sele==true) {
          Image($r('app.media.gou'))
            .width(30)
        }else{
          Image($r('app.media.circle'))
            .width(30)
        }
        Image($r('app.media.apple'))
          .width(100)
          .borderRadius(8)
          .margin({left:5})
        Column() {
          Column() {
            Text('纯天然大苹果')
              .lineHeight(20)
              .fontSize(20)
          }
          .margin({left:55,top:22})

          Row() {
            Text() {
              Span('￥')
                .fontSize(14)
              Span(this.price.toFixed(2))
                .fontSize(18)
            }
            .margin(30)
            .fontColor('#ff4000')
            if(this.count>1) {
              Text('-')
                .width(22)
                .height(22)
                .border({ width: 1, color: '#99CC33' })
                .textAlign(TextAlign.Center)
                .fontWeight(700)
                .onClick(() => {
                  this.count--
                })
            }else{
              Text('-')
                .width(22)
                .height(22)
                .border({ width: 1, color: '#F4F8FF' })
                .textAlign(TextAlign.Center)
                .fontWeight(700)
            }
            Text(this.count.toString())
              .margin(10)
              .height(22)
              .padding({ left: 10, right: 10 })
              .fontSize(14)
            Text('+')
              .width(22)
              .height(22)
              .border({ width: 1, color: '#99CC33' })
              .textAlign(TextAlign.Center)
              .fontWeight(700)
              .onClick(() => {
                this.count++
              })
          }

        }

      }
      .justifyContent(FlexAlign.Start)
      .width('100%')
      .height(100)
      .backgroundColor('#fff')
      .margin(10)
      .onClick(()=>{
        this.is_sele=!this.is_sele
      })

      //结算

      Row({ space: 20 }) {
        if(this.is_sele==true) {
          Image($r('app.media.gou'))
            .width(30)
            .onClick(()=>{
              this.is_sele=!this.is_sele
            })
        }else{
          Image($r('app.media.circle'))
            .width(30)
            .onClick(()=>{
              this.is_sele=!this.is_sele
            })
        }
        if (this.edit == 1) {
          Text('全选')
            .margin({ left: 5 })
            .fontSize(15)
            .onClick(()=>{
              this.is_sele=!this.is_sele
            })
          Text() {
            Span('合计:')
            Span('￥')
            Span((this.count * this.price).toFixed(2))
          }
          .fontSize(15)

          Button('结算')
            .width(110)
            .height(40)
            .backgroundColor('#90EE90')
            .margin(20)
            .onClick(() => {
              router.pushUrl({
                url: 'pages/settle'
              })
            })
        }else{
          Text('全选')
            .margin({ left: 5 })
            .fontSize(15)
            .onClick(()=>{
              this.is_sele=!this.is_sele
            })
          Button('移除')
            .width(80)
            .height(30)
            .border({width:1,color:'#F3F7FE'})
            .padding(5)
            .fontColor('#FF0410')
            .backgroundColor('#ffffff')
            .margin(160)
            .onClick(() => {
              this.edit=1
            })
        }
      }
      .margin({ top: 352 })
      .height(52)
      .width('100%')
      .backgroundColor('#ffffff')
    }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .width('100%')
    .height('100%')
    .backgroundColor('#F4F8FF')
  }
}