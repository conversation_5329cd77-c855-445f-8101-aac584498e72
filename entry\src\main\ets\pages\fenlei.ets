import router from '@ohos.router';

//干果组件
@Component
struct ganguo {



  build(){
    Column(){
      Text('干果的内容')

    }
  }
}

//粮油组件
@Component
struct liangyou {
  build(){
    Column(){
      Text('粮油的内容')

    }
  }
}

//蔬菜组件
@Component
struct shucai {
  build(){
    Column(){
      Text('蔬菜的内容')

    }
  }
}

//推荐组件
@Component
struct tuijian {
  build(){
    Column(){
      Text('推荐的内容')

    }
  }
}

//水果组件
@Component
struct shuiguo {
  build(){
    //下面物品列表
    Row({space:35}){
      Column() {
        Column({space:15}){
          Image($r('app.media.redapple'))
            .width(100)
            .height(100)
          Text('红苹果')
        }

        Column({space:15}){
          Image($r('app.media.greenapple'))
            .width(100)
            .height(100)
          Text('绿苹果')
        }

        Column({space:15}){
          Image($r('app.media.hamigua'))
            .width(100)
            .height(100)
          Text('哈密瓜')
        }

      }

      Column() {
        Column({space:15}){
          Image($r('app.media.banana'))
            .width(100)
            .height(100)
          Text('海南香蕉')
        }

        Column({space:15}){
          Image($r('app.media.caomei'))
            .width(100)
            .height(100)
          Text('草莓')
        }

        Column({space:15}){
          Image($r('app.media.putao'))
            .width(100)
            .height(100)
          Text('葡萄')
        }

      }
    }.width(300)
    .onClick(() => {
      router.pushUrl({
        url: 'pages/applePage'
      })
    })
  }
}

@Component
export struct fenlei {
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  //定义swiper控制器
  swiper:SwiperController = new SwiperController()


  //定义变量表示当前swiper的下标
  @State currentIndex:number=0





  build() {
    Column({ space: 10 }) {
      //顶部搜索状态栏

      Row({space:20}){

        Stack({alignContent:Alignment.Start}){
          TextInput({placeholder:'精选天然水果'})
            .padding({left:40,right:70,top:10,bottom:10})
            .height(40)
            .backgroundColor('#ffffff')
            .borderColor('#999999')
            .borderWidth(1)

          Image($r('app.media.sousuo'))
            .width(16).height(16)
            .margin({left:10})
        }
        .width(200)

        //分享和回主页
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:12}); // 调整右边距和顶边距
      }
      .backgroundColor('#99CC33')
      .width('100%')
      .height(80)
      .borderRadius(8)
      .padding(10)

      //下面部分
      Row() {
        //左边导航栏
        Column({ space: 45 }) {

          Text('推荐')
            .fontSize(this.currentIndex == 0 ? 20:18)
            .fontColor(this.currentIndex == 0 ? '#000000' :'#999999')
            .onClick(()=>{
              this.swiper.changeIndex(0)
            })
            .width(80)
            .height(30)
            .padding(10)
            .backgroundColor('#FFFFFF')
            .borderRadius(8)

          Text('蔬菜类')
            .fontSize(this.currentIndex == 1 ? 20:18)
            .fontColor(this.currentIndex == 1 ? '#000000' :'#999999')
            .onClick(()=>{
              this.swiper.changeIndex(1)
            })
            .width(80)
            .height(30)
            .padding(10)
            .backgroundColor('#FFFFFF')
            .borderRadius(8)

          Text('干果类')
            .fontSize(this.currentIndex == 2 ? 20:18)
            .fontColor(this.currentIndex == 2 ? '#000000' :'#999999')
            .onClick(()=>{
              this.swiper.changeIndex(2)
            })
            .width(80)
            .height(30)
            .padding(10)
            .backgroundColor('#FFFFFF')
            .borderRadius(8)

          Text('粮油类')
            .fontSize(this.currentIndex == 3 ? 20:18)
            .fontColor(this.currentIndex == 3 ? '#000000' :'#999999')
            .onClick(()=>{
              this.swiper.changeIndex(3)
            })
            .width(80)
            .height(30)
            .padding(10)
            .backgroundColor('#FFFFFF')
            .borderRadius(8)

          Text('水果类')
            .fontSize(this.currentIndex == 4 ? 20:18)
            .fontColor(this.currentIndex == 4 ? '#000000' :'#999999')
            .onClick(()=>{
              this.swiper.changeIndex(4)
            })
            .width(80)
            .height(30)
            .padding(10)
            .backgroundColor('#FFFFFF')
            .borderRadius(8)
        }
        .height('100%')

        //右边部分
        Column({ space: 15 }) {
          //上面轮播图
          Swiper(this.swiper) {
            Image($r('app.media.lunbo'))
            Image($r('app.media.lunbo'))
            Image($r('app.media.lunbo'))
          }
          .autoPlay(true)
          .width(260)
          .height(100)
          .borderRadius(15)


          Swiper(this.swiper){

            shuiguo()
            shuiguo()
            shuiguo()
            shuiguo()
            shuiguo()

          }
          .width(260)
          .onChange(index=>{
            this.currentIndex =index
          })
          .indicator(false) // 关键设置：隐藏指示点
        }
        .height('100%')


      }.justifyContent(FlexAlign.Start)
      .width('100%')



    }.justifyContent(FlexAlign.Start).padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .height('100%')
    .width('100%')


  }
}