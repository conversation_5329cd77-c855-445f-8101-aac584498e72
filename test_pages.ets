// 测试页面 - 用于验证动态功能
import { router } from '@kit.ArkUI';

@Entry
@Component
struct TestPages {
  build() {
    Column({ space: 20 }) {
      Text('动态功能测试')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 })

      Button('打开动态首页 (shouye)')
        .width('80%')
        .height(50)
        .backgroundColor('#99CC33')
        .onClick(() => {
          router.pushUrl({ url: 'shouye' });
        })

      But<PERSON>('打开动态详情页 (dongtai)')
        .width('80%')
        .height(50)
        .backgroundColor('#99CC33')
        .onClick(() => {
          // 模拟动态数据
          const mockPost = {
            id: 1,
            username: 'Alice',
            content: '毕业快乐',
            created_at: '2024-01-01 12:00:00',
            likes_count: 66,
            comments_count: 3,
            shares_count: 4
          };
          
          router.pushUrl({
            url: 'pages/dongtai',
            params: { post: JSON.stringify(mockPost) }
          });
        })

      Text('使用说明：')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .margin({ top: 30 })

      Text('1. 点击动态首页按钮查看动态列表\n2. 在首页点击动态可进入详情页\n3. 在详情页可以点赞和评论\n4. 返回首页会自动刷新数据')
        .fontSize(14)
        .fontColor('#666666')
        .lineHeight(20)
        .padding(20)
        .backgroundColor('#f8f9fa')
        .borderRadius(12)
        .width('90%')
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .backgroundColor('#ffffff')
  }
}
