import router from '@ohos.router';

interface  Item1{
  img:Resource
  name:string
  interpret:string
  price:string
  sellnum:string

}
//推荐组件
@Component
struct zonghe {
  //数据，模拟数据未来后端提供
  datas: Item1[] = [

    {
      img: $r('app.media.apple1'),
      name: '湖北红苹果',
      interpret: '好吃的很好吃的红苹果',
      price: '21.5元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple2'),
      name: '湖南红苹果',
      interpret: '好吃的很好吃的红苹果',
      price: '32.5元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple3'),
      name: '美国红苹果',
      interpret: '不好吃的很不好吃的红苹果',
      price: '1.5元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple4'),
      name: '日本红苹果',
      interpret: '好吃的很好吃的红苹果',
      price: '2.35元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple5'),
      name: '非洲红苹果',
      interpret: '很好吃的红黑黑黑苹果',
      price: '2.85元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple6'),
      name: '东北红苹果',
      interpret: '好吃的很好吃的东北苹果',
      price: '22.5元',
      sellnum: '100万斤'
    },
  ]
  datas2: Item1[] = [

    {
      img: $r('app.media.apple7'),
      name: '大苹果',
      interpret: '好吃的很好吃的红苹果',
      price: '21.5元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple8'),
      name: '小红苹果',
      interpret: '好吃的很好吃的红苹果',
      price: '32.5元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple9'),
      name: '长红苹果',
      interpret: '不好吃的很不好吃的红苹果',
      price: '1.5元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.apple10'),
      name: '矮红苹果',
      interpret: '好吃的很好吃的红苹果',
      price: '2.35元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.redapple'),
      name: '胖红苹果',
      interpret: '很好吃的红黑黑黑苹果',
      price: '2.85元',
      sellnum: '100万斤'
    },
    {
      img: $r('app.media.greenapple'),
      name: '瘦苹果',
      interpret: '好吃的很好吃的东北苹果',
      price: '22.5元',
      sellnum: '100万斤'
    },
  ]

  //UI
  @Builder
  myItemUI(item: Item1) {

    Column({ space: 10 }) {
      Image(item.img)
        .width(150)
        .height(80)
        .borderRadius(12)

      Text(item.name)
        .fontSize(30)
        .fontWeight(400)

      Text(item.interpret)
        .fontSize(15)
        .fontWeight(100)

      Row({ space: 30 }) {
        Text(item.price)
          .fontSize(25)
          .fontWeight(300)
          .fontColor('#fff5072b')

        Text(item.sellnum)
          .fontSize(15)
          .fontWeight(100)
      }

    }
    .width(170)
    .height(210)
    .borderRadius(15)
    .backgroundColor('#ffe8fcdf')
    .margin({ top: 20 })
  }

  //定义变量表示是否正在下拉刷新
  @State isRefresh: boolean = false;
  //定义变量表示是否正在上拉加载
  @State isLoading: boolean = false;

  build() {
    Row({space:10}) {
      Refresh({ refreshing: $$this.isRefresh, promptText: '正在刷新中...' }) {
        // Refresh中只包含一个List组件
        List() {

          ListItem() {
            Row({space:30}) {
              // 第一组数据
              Column({ space: 5 }) {
                ForEach(this.datas, (item: Item1) => {
                  this.myItemUI(item)
                })
              }
              .width(163)
              // 第二组数据
              Column({ space: 5 }) {
                ForEach(this.datas2, (item: Item1) => {
                  this.myItemUI(item)
                })
              }
              .width(163)
            }
          }
          .onClick(() => {
            router.pushUrl({
              url: 'pages/yishangpinPage'
            })
          })
          .margin({left:15,right:15})


          // 第二组数据



          // 加载提示应作为List的子元素
          if (this.isLoading) {
            ListItem() {
              Row({ space: 5 }) {
                Text('拼命加载中...')
                LoadingProgress()
                  .width(20)
                  .height(20)
              }
              .justifyContent(FlexAlign.Center)
              .padding(20)
              .width('100%')
            }
          }
        }
        // 使用正确的滚动监听方法
        .onReachEnd(() => {
          if (!this.isLoading) {
            this.isLoading = true
            // 模拟加载更多数据
            setTimeout(() => {
              this.datas2.push(...[
              // 添加模拟的新数据

              ])
              this.isLoading = false;
            }, 1500)
          }
        })
        .scrollBar(BarState.Off)
      }
      .onRefreshing(() => {
        // 刷新逻辑...
        setTimeout(() => {
          this.isRefresh = false;
        }, 2000)
      })
    }
    .width('100%')
  }


}
//销量组件
@Component
struct xiaoliang {
  build(){
    Column(){
      Text('销量的内容')

    }
  }
}
//口碑组件
@Component
struct koubei {
  build(){
    Column(){
      Text('口碑的内容')

    }
  }
}
//筛选组件
@Component
struct saixuan {
  @State selectedPrice: string = '';
  @State selectedDelivery: string = '';

  build(){
    Column({space: 20}) {
      // 人均消费
      Column({space: 10}) {
        Row() {
          Rect()
            .width(4)
            .height(16)
            .fill('#99CC33')
          Text('人均消费')
            .fontSize(16)
            .fontWeight(500)
        }
        .alignItems(VerticalAlign.Center)
        
        Row({space: 10}) {
          Text('10元以下')
            .fontSize(14)
            .padding({left: 15, right: 15, top: 8, bottom: 8})
            .backgroundColor(this.selectedPrice === '10元以下' ? '#99CC33' : '#F5F5F5')
            .fontColor(this.selectedPrice === '10元以下' ? '#FFFFFF' : '#666666')
            .borderRadius(20)
            .onClick(() => {
              this.selectedPrice = this.selectedPrice === '10元以下' ? '' : '10元以下'
            })
          
          Text('10-30元')
            .fontSize(14)
            .padding({left: 15, right: 15, top: 8, bottom: 8})
            .backgroundColor(this.selectedPrice === '10-30元' ? '#99CC33' : '#F5F5F5')
            .fontColor(this.selectedPrice === '10-30元' ? '#FFFFFF' : '#666666')
            .borderRadius(20)
            .onClick(() => {
              this.selectedPrice = this.selectedPrice === '10-30元' ? '' : '10-30元'
            })
          
          Text('30元以上')
            .fontSize(14)
            .padding({left: 15, right: 15, top: 8, bottom: 8})
            .backgroundColor(this.selectedPrice === '30元以上' ? '#99CC33' : '#F5F5F5')
            .fontColor(this.selectedPrice === '30元以上' ? '#FFFFFF' : '#666666')
            .borderRadius(20)
            .onClick(() => {
              this.selectedPrice = this.selectedPrice === '30元以上' ? '' : '30元以上'
            })
        }
      }
      .alignItems(HorizontalAlign.Start)
      
      // 商品配送
      Column({space: 10}) {
        Row() {
          Rect()
            .width(4)
            .height(16)
            .fill('#99CC33')
          Text('商品配送')
            .fontSize(16)
            .fontWeight(500)
        }
        .alignItems(VerticalAlign.Center)
        
        Row({space: 10}) {
          Text('顺丰快递')
            .fontSize(14)
            .padding({left: 15, right: 15, top: 8, bottom: 8})
            .backgroundColor(this.selectedDelivery === '顺丰快递' ? '#99CC33' : '#F5F5F5')
            .fontColor(this.selectedDelivery === '顺丰快递' ? '#FFFFFF' : '#666666')
            .borderRadius(20)
            .onClick(() => {
              this.selectedDelivery = this.selectedDelivery === '顺丰快递' ? '' : '顺丰快递'
            })
          
          Text('圆通快递')
            .fontSize(14)
            .padding({left: 15, right: 15, top: 8, bottom: 8})
            .backgroundColor(this.selectedDelivery === '圆通快递' ? '#99CC33' : '#F5F5F5')
            .fontColor(this.selectedDelivery === '圆通快递' ? '#FFFFFF' : '#666666')
            .borderRadius(20)
            .onClick(() => {
              this.selectedDelivery = this.selectedDelivery === '圆通快递' ? '' : '圆通快递'
            })
          
          Text('申通快递')
            .fontSize(14)
            .padding({left: 15, right: 15, top: 8, bottom: 8})
            .backgroundColor(this.selectedDelivery === '申通快递' ? '#99CC33' : '#F5F5F5')
            .fontColor(this.selectedDelivery === '申通快递' ? '#FFFFFF' : '#666666')
            .borderRadius(20)
            .onClick(() => {
              this.selectedDelivery = this.selectedDelivery === '申通快递' ? '' : '申通快递'
            })
        }
      }
      .alignItems(HorizontalAlign.Start)
      
      // 底部按钮
      Row({space: 20}) {
        Text('清空')
          .fontSize(16)
          .fontColor('#666666')
          .textAlign(TextAlign.Center)
          .width(80)
          .height(40)
          .backgroundColor('#F5F5F5')
          .borderRadius(20)
          .onClick(() => {
            this.selectedPrice = ''
            this.selectedDelivery = ''
          })
        
        Text('确定')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .textAlign(TextAlign.Center)
          .width(200)
          .height(40)
          .backgroundColor('#99CC33')
          .borderRadius(20)
          .onClick(() => {
            // 确定筛选逻辑
          })
      }
      .justifyContent(FlexAlign.Center)
      .margin({top: 30})
    }
    .padding(20)
    .width('100%')
  }
}
@Entry
@Component
struct ApplePage {
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  //Swiper控制器
  swiperController:SwiperController = new SwiperController();
  //定义变量记录当前Swiper的下标
  @State currentIndex:number = 0;
  //控制筛选弹窗显示
  @State showFilter: boolean = false;
  //筛选状态
  @State selectedPrice: string = '';
  @State selectedDelivery: string = '';

  build() {
    //最外层
    Stack() {
      Column() {
        //顶部搜索栏
        Row({space:20}){
          //返回键
          Image($r('app.media.fanhui1'))
            .width(20)
            .height(20)
            .onClick(() => {
              router.back()
            })
            /*.onClick(() => {
              router.pushUrl({
                url: 'pages/Index'
              })
            })*/

          Stack({alignContent:Alignment.Start}){
            TextInput({placeholder:'精选天然水果'})
              .padding({left:40,right:70,top:10,bottom:10})
              .height(40)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)

            Image($r('app.media.sousuo'))
              .width(16).height(16)
              .margin({left:10})
          }
          .width(200)

          //分享和回主页
          Row({
            space: 10,
          }) {
            Image($r('app.media.pot'))
              .width(24)
              .fillColor('#ffffff');

            Divider()
              .vertical(true)
              .strokeWidth(1)
              .color('#ffffff')
              .height(22)

            Image($r('app.media.exitcircle'))
              .width(24)
              .fillColor('#ffffff');
          }
          .border({
            width: 1,
            radius:20,
            color: '#6B8F24' // 淡黑色边框
          })
          .padding({ left: 20, right: 20,top: 8,bottom:8 })
          .backgroundColor('#6B8F24') // 淡黑色背景
          .margin({left:12}); // 调整右边距和顶边距
        }
        .backgroundColor('#99CC33')
        .width('100%')
        .height(80)
        .borderRadius(8)
        .padding(10)

        //导航栏 综合销量等
        Row(){
          Text('综合')
            .fontColor(this.currentIndex == 0 ? '#000000':'#999999')
            .fontSize(this.currentIndex == 0 ? '20' : '18')
            .onClick(()=>{
              this.currentIndex = 0
              this.swiperController.changeIndex(0)
            })

          Text('销量')
            .fontColor(this.currentIndex == 1 ? '#000000':'#999999')
            .fontSize(this.currentIndex == 1 ? '20' : '18')
            .onClick(()=>{
              this.currentIndex = 1
              this.swiperController.changeIndex(1)
            })
          Text('口碑')
            .fontColor(this.currentIndex == 2 ? '#000000':'#999999')
            .fontSize(this.currentIndex == 2 ? '20' : '18')
            .onClick(()=>{
              this.currentIndex = 2
              this.swiperController.changeIndex(2)
            })
          Row({space:5}) {
            Text('筛选')
              .fontColor('#999999')
              .fontSize('18')
              .onClick(() => {
                this.showFilter = true
              })
            Image($r('app.media.saixuan'))
              .width(24)
              .height(24)
              .onClick(() => {
                this.showFilter = true
              })
          }
        }
        .padding(10)
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)

        Swiper(this.swiperController){
          zonghe()
          zonghe()
          zonghe()
          zonghe()
        }
        .onChange(index=>{
          this.currentIndex = index
        })
        .indicator(false)
        .width('100%')
        .layoutWeight(1)
      }
      .width('100%')
      .height('100%')

      // 筛选悬浮层
      if (this.showFilter) {
        Column() {
          // 筛选内容区域 - 从顶部开始
          Column({space: 20}) {
            // 人均消费
            Column({space: 10}) {
              Row() {
                Rect()
                  .width(4)
                  .height(16)
                  .fill('#99CC33')
                Text('人均消费')
                  .fontSize(16)
                  .fontWeight(500)
              }
              .alignItems(VerticalAlign.Center)
              
              Row({space: 10}) {
                Text('10元以下')
                  .fontSize(14)
                  .padding({left: 15, right: 15, top: 8, bottom: 8})
                  .backgroundColor(this.selectedPrice === '10元以下' ? '#99CC33' : '#F5F5F5')
                  .fontColor(this.selectedPrice === '10元以下' ? '#FFFFFF' : '#666666')
                  .borderRadius(20)
                  .onClick(() => {
                    this.selectedPrice = this.selectedPrice === '10元以下' ? '' : '10元以下'
                  })
                
                Text('10-30元')
                  .fontSize(14)
                  .padding({left: 15, right: 15, top: 8, bottom: 8})
                  .backgroundColor(this.selectedPrice === '10-30元' ? '#99CC33' : '#F5F5F5')
                  .fontColor(this.selectedPrice === '10-30元' ? '#FFFFFF' : '#666666')
                  .borderRadius(20)
                  .onClick(() => {
                    this.selectedPrice = this.selectedPrice === '10-30元' ? '' : '10-30元'
                  })
                
                Text('30元以上')
                  .fontSize(14)
                  .padding({left: 15, right: 15, top: 8, bottom: 8})
                  .backgroundColor(this.selectedPrice === '30元以上' ? '#99CC33' : '#F5F5F5')
                  .fontColor(this.selectedPrice === '30元以上' ? '#FFFFFF' : '#666666')
                  .borderRadius(20)
                  .onClick(() => {
                    this.selectedPrice = this.selectedPrice === '30元以上' ? '' : '30元以上'
                  })
              }
            }
            .alignItems(HorizontalAlign.Start)
            
            // 商品配送
            Column({space: 10}) {
              Row() {
                Rect()
                  .width(4)
                  .height(16)
                  .fill('#99CC33')
                Text('商品配送')
                  .fontSize(16)
                  .fontWeight(500)
              }
              .alignItems(VerticalAlign.Center)
              
              Row({space: 10}) {
                Text('顺丰快递')
                  .fontSize(14)
                  .padding({left: 15, right: 15, top: 8, bottom: 8})
                  .backgroundColor(this.selectedDelivery === '顺丰快递' ? '#99CC33' : '#F5F5F5')
                  .fontColor(this.selectedDelivery === '顺丰快递' ? '#FFFFFF' : '#666666')
                  .borderRadius(20)
                  .onClick(() => {
                    this.selectedDelivery = this.selectedDelivery === '顺丰快递' ? '' : '顺丰快递'
                  })
                
                Text('圆通快递')
                  .fontSize(14)
                  .padding({left: 15, right: 15, top: 8, bottom: 8})
                  .backgroundColor(this.selectedDelivery === '圆通快递' ? '#99CC33' : '#F5F5F5')
                  .fontColor(this.selectedDelivery === '圆通快递' ? '#FFFFFF' : '#666666')
                  .borderRadius(20)
                  .onClick(() => {
                    this.selectedDelivery = this.selectedDelivery === '圆通快递' ? '' : '圆通快递'
                  })
                
                Text('申通快递')
                  .fontSize(14)
                  .padding({left: 15, right: 15, top: 8, bottom: 8})
                  .backgroundColor(this.selectedDelivery === '申通快递' ? '#99CC33' : '#F5F5F5')
                  .fontColor(this.selectedDelivery === '申通快递' ? '#FFFFFF' : '#666666')
                  .borderRadius(20)
                  .onClick(() => {
                    this.selectedDelivery = this.selectedDelivery === '申通快递' ? '' : '申通快递'
                  })
              }
            }
            .alignItems(HorizontalAlign.Start)
            
            // 底部按钮
            Row({space: 20}) {
              Text('清空')
                .fontSize(16)
                .fontColor('#666666')
                .textAlign(TextAlign.Center)
                .width(80)
                .height(40)
                .backgroundColor('#F5F5F5')
                .borderRadius(20)
                .onClick(() => {
                  this.selectedPrice = ''
                  this.selectedDelivery = ''
                })
              
              Text('确定')
                .fontSize(16)
                .fontColor('#FFFFFF')
                .textAlign(TextAlign.Center)
                .width(200)
                .height(40)
                .backgroundColor('#99CC33')
                .borderRadius(20)
                .onClick(() => {
                  this.showFilter = false
                })
            }
            .justifyContent(FlexAlign.Center)
            .margin({top: 30})
          }
          .padding(20)
          .width('100%')
          .backgroundColor('#FFFFFF')
          .borderRadius({bottomLeft: 16, bottomRight: 16})
          .margin({top: 140})

          // 透明遮罩层 - 点击关闭
          Column()
            .width('100%')
            .layoutWeight(1)
            .backgroundColor('rgba(0,0,0,0.3)')
            .onClick(() => {
              this.showFilter = false
            })
        }
        .width('100%')
        .height('100%')
      }
    }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .width('100%')
    .height('100%')
  }

}
