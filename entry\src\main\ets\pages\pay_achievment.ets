import router from '@ohos.router'

@Entry
@Component

struct pay_achievment {
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  build() {
    Column() {
      Row() {
        Image($r("app.media.return"))
          .width(20)
          .margin({ left: 10, top: 5 })
          .fillColor('#ffffff')
          .onClick(() => {
            router.back()
          })
        Text('支付成功')
          .fontColor('#ffffff')
          .margin({ left: 80, top: 5 })
          .fontSize(22)
          .fontWeight(88)
        Row({
          space: 10
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({
          left: 20,
          right: 20,
          top: 8,
          bottom: 8
        })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({ left: 22, right: 10, top: 5 });
      }
      .justifyContent(FlexAlign.SpaceBetween)
      .width('100%')
      .padding({ left: 5, right: 5 })
      .height(60)
      .backgroundColor('#99CC33')



      // 按钮区域
      Row() {
        Column({ space: 20 }) {
          Image($r('app.media.gou')) // 成功图标
            .width(50)
            .height(50)
            .fillColor('#99CC33')
          Text('135.00元，支付成功！')
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .margin({ top: 10 });
          Row({space:20}) {
            Button('返回首页')
              .fontColor('#000000')
              .width(120)
              .height(40)
              .backgroundColor('#ffffff')
              .border({width:1,radius:1,color:'F4F8FF'})

            Button('查看订单')
              .width(120)
              .height(40)
              .borderRadius(2)
              .backgroundColor('#99CC33')

          }
        }
        .padding(20)
      }
      .backgroundColor('#ffffff')
      .justifyContent(FlexAlign.Center)
      .width('100%')
      .padding(20);

      // 分割线
      Divider()
        .strokeWidth(1)
        .color('#ccc');

      // 更多推荐标题
      Row() {
        Image($r('app.media.recommend')) // 推荐图标
          .width(20)
          .height(20);
        Text('更多推荐')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .margin({ left: 5, right: 5 });
        Image($r('app.media.recommend')) // 推荐图标
          .width(20)
          .height(20);
      }
      .padding(10);

      // 商品推荐列表
      Column({ space: 10 }) {
        Row({ space: 10 }) {
          Image($r(`app.media.apple`)) // 商品图片
            .width(80)
            .height(80)
            .borderRadius(5);

          Column({ space: 5 }) {
            Text('纯天然水果（500g）')
              .fontSize(14)
            Text('纯天然种植 | 自然生长 | 绿色食品')
              .fontSize(12)
              .fontColor('#F4F8FF')
            Text('¥ 60.00')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
          }
          .alignItems(HorizontalAlign.Start)
        }
        .width('100%')
        .padding(10)
        .borderRadius(5)
        .backgroundColor('#f9f9f9')

        Row({ space: 10 }) {
          Image($r(`app.media.apple`)) // 商品图片
            .width(80)
            .height(80)
            .borderRadius(5);

          Column({ space: 5 }) {
            Text('纯天然水果（500g）')
              .fontSize(14)
            Text('纯天然种植 | 自然生长 | 绿色食品')
              .fontSize(12)
              .fontColor('#F4F8FF')
            Text('¥ 60.00')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
          }
          .alignItems(HorizontalAlign.Start)
        }
        .width('100%')
        .padding(10)
        .borderRadius(5)
        .backgroundColor('#f9f9f9')


        Row({ space: 10 }) {
          Image($r(`app.media.apple`)) // 商品图片
            .width(80)
            .height(80)
            .borderRadius(5)

          Column({ space: 5 }) {
            Text('纯天然水果（500g）')
              .fontSize(14)
            Text('纯天然种植 | 自然生长 | 绿色食品')
              .fontSize(12)
              .fontColor('#F4F8FF')
            Text('¥ 60.00')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
          }
          .alignItems(HorizontalAlign.Start)
        }
        .width('100%')
        .justifyContent(FlexAlign.Start)
        .padding(10)
        .borderRadius(5)
        .backgroundColor('#f9f9f9')

      }
      .padding(10)
    }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .width('100%')
    .height('100%')
    .backgroundColor('#F4F8FF')
  }
}