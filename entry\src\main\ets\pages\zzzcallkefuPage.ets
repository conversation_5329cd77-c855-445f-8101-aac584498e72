import router from '@ohos.router';
@Entry
@Component
struct zzzcallkefuPage {



  build() {
    //最外层
    Column() {
      //顶部搜索栏
      Row() {

        //返回键

        Image($r('app.media.fanhui1'))

          .width(20)
          .height(20)
          .onClick(() => {
            router.back()
          })


        Text('联系客服')
          .fontSize(25)
          .fontColor('#ffffff')
          .margin({left:100})

        //.margin({right:100})
        //分享和回主页
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:40}); // 调整右边距和顶边距

      }
      .backgroundColor('#99CC33')
      .width('100%')
      .height(80)
      .borderRadius(8)
      .padding(10)

      //中间聊天界面
      Image($r('app.media.zzzliaotian'))
        .width('100%')
        .height(630)
        .margin('head:30')

      //输入框
      Row({space:20}){
        //输入框
        TextInput({placeholder:'请输入您要咨询的内容'})
          .width(280)
          .padding({left:5,right:70,top:10,bottom:10})
          .height(40)
          .backgroundColor('#ffffff')
          .borderColor('#999999')
          .borderWidth(1)

        //发送按钮
        Button() {
          Text('    发送')
            .fontColor('#ffffff')
            .fontSize(15)
            .fontWeight(80)
            .backgroundColor('##99CC33')
            .borderRadius(15)
            .width(60)
            .height(40)
            .backgroundColor("#99CC33") // 背景色

        }
        .width(60)
        .height(40)
      }
      .width('100%')
      .padding({left:20,right:70,top:10,bottom:10})



    }
  }
}