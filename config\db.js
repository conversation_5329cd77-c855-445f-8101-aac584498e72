require('dotenv').config();
const mysql = require('mysql2/promise');
// console.log("host:",process.env.DB_HOST);

const pool = mysql.createPool({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// console.log("pool:", pool);
//创建用户信息表
async function initializeDatabase() {
    try {
        const connection = await pool.getConnection();
        await connection.query(`
    CREATE TABLE IF NOT EXISTS users(
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    creatde_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    `);
        //添加城市天气的表
        await connection.query(`
    CREATE TABLE IF NOT EXISTS citys(
    id INT AUTO_INCREMENT PRIMARY KEY,
    cityCode VARCHAR(255) NOT NULL UNIQUE,
    cityName VARCHAR(255) NOT NULL,
    
    creatde_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    `);
        connection.release();
        console.log(`Database initialize`);
    } catch (error) {
        console.error(`Database initialize failed`, error);
    }
}

module.exports = { pool, initializeDatabase };