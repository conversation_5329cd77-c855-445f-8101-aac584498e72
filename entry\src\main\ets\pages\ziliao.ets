import { router, Router } from "@kit.ArkUI";
// 定义用户信息接口
interface UserInfo {
  account: string;
  nickname: string;
  gender: string;
  age: number;
}

@Entry
@Component
export struct ziliao {
  @State message: string = 'Hello World';

  @StorageProp('bottomRectHeight') bottomRectHeight: number = 0;
  @StorageProp('topRectHeight') topRectHeight: number = 0;

  @State userInfo: UserInfo = {
    account: '180****0626',
    nickname: 'Zero',
    gender: '男',
    age: 30
  };

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Image($r('app.media.u2333'))
          .width(20)
          .height(20)
          .margin({ left: 15, bottom:1 })
          .onClick(() => {
            router.back();
          });

        Text('账号资料')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#FFFFFF')
          .margin({ left: 120, bottom:1 });


        // 右侧按钮组
        Row({ space: 10 }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22);

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24'
        })
        .padding({ left: 20, right: 20, top: 8, bottom: 8 })
        .backgroundColor('#6B8F24')
        .margin({ left:30,right:5,  bottom: 1 });
      }
      .backgroundColor('#99CC33')
      .height(100)
      .padding({top:35,bottom:1})
      .width('100%');

      // 头像区域（使用 Stack 垂直居中）
      Stack({ alignContent: Alignment.Center }) {
        Image($r('app.media.u2350'))
          .width(70)
          .height(70)
          .margin({ top: 20 });

        Image($r('app.media.u2351'))
          .width(20)
          .height(20)
          .margin({ top: 15, left: 13});
      }
      .width('100%')
      .height(100);

      // 信息展示区域
      Column() {
        this.buildInfoItem('账号', this.userInfo.account);
        this.buildInfoItem('昵称', this.userInfo.nickname);
        this.buildInfoItem('性别', this.userInfo.gender);
        this.buildInfoItem('年龄', this.userInfo.age.toString());
      }
      .width('90%')
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
      .padding(12)

      .margin({ left: '5%', right: '5%' });
    }
    .backgroundColor('#F4F8FF')
    .width('100%')
    .height('100%');
  }

  // 构建信息项组件
  @Builder
  buildInfoItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .width(80)


      Text(value)
        .fontSize(14)
        .textAlign(TextAlign.End)
        .flexGrow(1)
        .fontColor('#666666');
    }
    .width('100%')
    .padding(20)
    .borderRadius(15);
  }
}




