const express = require('express');
const router = express.Router();
const { register, login, getCurrentUser,test } = require('../controllers/userController');
const { authenticate } = require('../middlewares/authMiddleware');

// 公共路由
router.post('/register', register);
router.post('/login', login);

router.get('/test', test);
// 获取当前用户信息
router.get('/me', authenticate, getCurrentUser);

module.exports = router;