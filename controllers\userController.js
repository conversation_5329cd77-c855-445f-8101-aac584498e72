//依赖导入
const { pool } = require('../config/db');
require('dotenv').config();

//密码加密
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 用户注册
const register = async (req, res) => {
  try {
    console.log('register');
    const { username, password } = req.body;
    // 检查用户是否已存在
    const [existingUser] = await pool.query(
      'SELECT * FROM users WHERE username=? ',
      [username]
    );
    if (existingUser.length > 0) {
      return res.status(400).json({ code: 400, message: "用户名已存在" })
    }
    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 创建用户
    const [result] = await pool.query(
      'INSERT INTO users (username,password) VALUES (?,?)',
      [username, hashedPassword]
    );

    res.json({ code: 200, message: "注册成功" })
  } catch (error) {
    console.error(error);
    res.status(500).json("服务器错误")
  }
};
const test = async (req, res) => {
  console.log('test...');
  res.json({ code: 200, message: 'test ok' })
}

// 用户登录
const login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 检查用户是否存在
    const [users] = await pool.query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );
    //表示用户不存在
    if (users.length === 0) {
      return res.status(400).json({ code: 400, message: "用户名不存在" });
    }
    const user = users[0];
    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ code: 400, message: "用户密码不正确" });

    }

    user.password = '';

    // 生成JWT
    // 生成JWT
    const payload = {
      user: {
        id: user.id,
        username: user.username
      }
    };
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '1h' },
      (err, token) => {
        if (err) throw err;
        res.json({ user: user, token: token });
      }
    );
  } catch (error) {
    console.error(error);
    res.status(500).json("服务器错误")
  }
};
// 获取当前用户信息
const getCurrentUser = async (req, res) => {
  try {
    const [users] = await pool.query(
      'SELECT id, username, created_at FROM users WHERE id = ?',
      [req.user.id]
    );
    if (users.length === 0) {
      return res.status(404).json({ code: 404, message: "用户不存在" });

    }

    res.json(users[0]);

  } catch (error) {
    console.error(error);
    res.status(500).json("服务器错误")
  }
};

module.exports = { register, login, getCurrentUser, test };