import { promptAction } from '@kit.ArkUI';
import { router } from '@kit.ArkUI';
import http from '@ohos.net.http';
import { util } from '@kit.ArkTS';

interface LoginRequest {
  phoneNumber: string;
  password: string;
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

interface JwtPayload {
  userId: string;
  exp: number;
  iat: number;
  sub?: string;
  username?: string;
}

@Entry
@Component
struct Login {
  @State phoneNumber: string = '';
  @State password: string = '';
  @State message: string = 'Hello World';
  @State codeValue: string = '';
  @State phoneFocused: boolean = false;
  @State codeFocused: boolean = false;
  @State isLoading: boolean = false;

  private baseUrl: string = 'http://192.168.31.34:8080/api';

  build() {
    Column() {
      Stack() {
        Image($r('app.media.starter1'))
          .width('100%')
          .height('100%')

        Column({ space: 20 }) {
          Text('登录')
            .fontSize(35)
            .fontWeight(300)

          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '手机号码', text: $$this.phoneNumber })
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(60)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.phoneFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.phoneFocused = true;
                this.codeFocused = false;
              })
              .onBlur(() => {
                this.phoneFocused = false;
              });

            Image($r('app.media.xiaoxi'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '密码', text: $$this.password })
              .type(InputType.Password)
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(60)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.codeFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.codeFocused = true;
                this.phoneFocused = false;
              })
              .onBlur(() => {
                this.codeFocused = false;
              });

            Image($r('app.media.u1915'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          Button(this.isLoading ? '登录中...' : '登录')
            .type(ButtonType.Normal)
            .backgroundColor('#F48C95')
            .width('100%')
            .fontColor('#ffffff')
            .fontSize(25)
            .fontWeight(600)
            .height(60)
            .borderRadius(18)
            .enabled(!this.isLoading)
            .onClick(() => {
              this.handleLogin();
            })

          Button('去注册')
            .type(ButtonType.Normal)
            .backgroundColor('#ffffff')
            .border({ width: 1, color: '#F48C95' })
            .width('100%')
            .fontColor('#F48C95')
            .fontSize(18)
            .height(50)
            .borderRadius(18)
            .onClick(() => {
              router.pushUrl({ url: 'pages/zhuce' });
            })
        }
        .padding(20)
        .margin({ top: 500 })
        .width('100%')
        .height('100%')
        .borderRadius(50)
        .backgroundColor('#ffffff')
      }
    }
    .height('100%')
    .width('100%')
  }

  private async handleLogin() {
    console.log('=== 开始登录流程 ===');

    if (!this.phoneNumber.trim()) {
      promptAction.showToast({ message: "请输入手机号" });
      return;
    }

    if (!this.password.trim()) {
      promptAction.showToast({ message: "请输入密码" });
      return;
    }

    console.log('手机号:', this.phoneNumber);
    console.log('密码长度:', this.password.length);

    this.isLoading = true;

    try {
      const httpRequest = http.createHttp();
      const loginRequest: LoginRequest = {
        phoneNumber: this.phoneNumber.trim(),
        password: this.password.trim()
      };
      const requestUrl = `${this.baseUrl}/login`;
      console.log('请求URL:', requestUrl);
      console.log('请求数据:', JSON.stringify(loginRequest));

      const response = await httpRequest.request(requestUrl, {
        method: http.RequestMethod.POST,
        header: {
          'Content-Type': 'application/json'
        },
        extraData: JSON.stringify(loginRequest),
        connectTimeout: 10000,
        readTimeout: 10000
      });

      console.log('=== 收到响应 ===');
      console.log('HTTP状态码:', response.responseCode);
      console.log('响应数据:', response.result);

      if (response.responseCode === 200) {
        const result: ApiResponse<string> = JSON.parse(response.result as string);
        console.log('解析后的结果:', JSON.stringify(result));

        if (result.code === 200) {
          console.log('登录成功，Token:', result.data);
          const userInfo = this.parseJwtToken(result.data);
          if (userInfo) {
            console.log('用户ID:', userInfo.userId);
            console.log('Token过期时间:', new Date(userInfo.exp * 1000));
          }
          await this.saveToken(result.data);
          promptAction.showToast({ message: "登录成功" });
          await this.navigateToIndex();
        } else {
          console.error('登录失败:', result.message);
          promptAction.showToast({
            message: result.message || "登录失败，请检查账号密码"
          });
        }
      } else {
        console.error('HTTP请求失败，状态码:', response.responseCode);
        promptAction.showToast({ message: "网络请求失败" });
      }

      httpRequest.destroy();
    } catch (error) {
      console.error('=== 登录异常 ===');
      console.error('异常信息:', JSON.stringify(error));
      promptAction.showToast({ message: "网络异常，请稍后重试" });
    } finally {
      this.isLoading = false;
      console.log('=== 登录流程结束 ===');
    }
  }

  private parseJwtToken(token: string): JwtPayload | null {
    try {
      console.log('开始解析JWT Token:', token);
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.error('JWT Token格式错误，应该包含3个部分');
        return null;
      }
      const payload = parts[1];
      console.log('JWT Payload部分:', payload);
      const decodedPayload = this.base64UrlDecode(payload);
      console.log('解码后的Payload:', decodedPayload);
      if (!decodedPayload) {
        console.error('Base64URL解码失败');
        return null;
      }
      const userInfo: JwtPayload = JSON.parse(decodedPayload);
      console.log('解析的用户信息:', JSON.stringify(userInfo));
      return userInfo;
    } catch (error) {
      console.error('JWT Token解析失败:', error);
      return null;
    }
  }

  private base64UrlDecode(str: string): string {
    try {
      let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
      while (base64.length % 4) {
        base64 += '=';
      }
      console.log('转换后的Base64字符串:', base64);
      const textDecoder = util.TextDecoder.create('utf-8');
      const base64Helper = new util.Base64Helper();
      const decodedArray = base64Helper.decodeSync(base64);
      const decoded = textDecoder.decodeWithStream(decodedArray);
      console.log('Base64解码结果:', decoded);
      return decoded;
    } catch (error) {
      console.error('Base64解码失败:', error);
      return '';
    }
  }

  private async saveToken(token: string): Promise<void> {
    try {
      console.log('保存Token到本地存储:', token);
    } catch (error) {
      console.error('Token保存失败:', error);
    }
  }

  private async navigateToIndex(): Promise<void> {
    try {
      await router.replaceUrl({ url: 'pages/Index' });
      console.log('页面跳转成功');
    } catch (error) {
      console.error('页面跳转失败:', error);
      promptAction.showToast({ message: "页面跳转失败" });
    }
  }
}