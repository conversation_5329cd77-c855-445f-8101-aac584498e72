import { App, router } from "@kit.ArkUI"


@Entry
@Component
export struct settle {
  @State count: number = 1
  @State freight: number = 15
  @State price: number = 60
  @State pswdnum :number[] =[1,2,3,4,5,6,7,8,9,0]
  //控制遮罩显影
  @State maskopacity :number=0
  @State maskzindex :number=-1
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  build() {
    Stack() {
      Column() {
        Row() {
          Image($r("app.media.return"))
            .width(20)
            .margin({ left: 10, top: 5 })
            .fillColor('#ffffff')
            .onClick(() => {
              router.back()
            })
          Text('确认订单')
            .fontColor('#ffffff')
            .margin({ left: 80, top: 5 })
            .fontSize(22)
            .fontWeight(88)
          Row({
            space: 10
          }) {
            Image($r('app.media.pot'))
              .width(24)
              .fillColor('#ffffff');

            Divider()
              .vertical(true)
              .strokeWidth(1)
              .color('#ffffff')
              .height(22)

            Image($r('app.media.exitcircle'))
              .width(24)
              .fillColor('#ffffff');
          }
          .border({
            width: 1,
            radius: 20,
            color: '#6B8F24' // 淡黑色边框
          })
          .padding({
            left: 20,
            right: 20,
            top: 8,
            bottom: 8
          })
          .backgroundColor('#6B8F24') // 淡黑色背景
          .margin({ left: 22, right: 10, top: 5 });
        }
        .justifyContent(FlexAlign.SpaceBetween)
        .width('100%')
        .padding({ left: 5, right: 5 })
        .height(60)
        .backgroundColor('#99CC33')

        Row() {
          //左侧列
          Column({ space: 12 }) {
            Text('湖北省荆州市学苑路汉科10086栋')
              .fontSize(20)
              .fontWeight(700)
              .margin({ top: 20, left: 10 })
            Text('xun先生 153****1234')
              .fontSize(15)
              .fontWeight(700)
              .fontColor('#999')
              .margin({ bottom: 20, left: 10 })
          }
          .alignItems(HorizontalAlign.Start)

          Row() {
            Image($r("app.media.right_arrow"))
              .width(20)
              .fillColor('#999')
          }
        }
        .justifyContent(FlexAlign.SpaceBetween)
        .width('100%')
        .height(100)
        .backgroundColor('#fff')
        .onClick(()=>{
          router.replaceUrl({url:'pages/myaddress'})
        })

        //商品列表
        Row() {
          Image($r('app.media.apple'))
            .width(100)
            .borderRadius(8)
            .margin({ left: 5 })
          Text('纯天然大苹果')
            .lineHeight(20)
            .fontSize(18)

          Column() {
            Text() {
              Span('￥')
                .fontSize(16)
              Span(this.price.toFixed(2))
                .fontSize(20)
            }

            Text('x1')
              .fontColor('#9CA6C4')
              .width(22)
              .height(22)
              .textAlign(TextAlign.Center)
              .fontWeight(700)
              .margin(22)

          }
          .margin({ left: 100, top: 22 })
        }
        .justifyContent(FlexAlign.Start)
        .width('100%')
        .height(100)
        .backgroundColor('#fff')
        .margin(10)

        //价格详细
        Row() {
          Text('商品总价')
            .fontSize(18)
            .margin({ left: 10 })
          Text('￥60.00')
            .margin({ left: 220 })
            .fontSize(18)
        }
        .justifyContent(FlexAlign.Start)
        .width('100%')
        .height(50)
        .backgroundColor('#fff')

        Row() {
          Text('运费')
            .fontSize(18)
            .margin({ left: 10 })
          Text() {
            Span('￥')
              .fontSize(18)
            Span(this.freight.toFixed(2))
              .fontSize(18)
            Span('(顺丰快递)')
              .fontSize(18)

          }
          .margin({ left: 180 })
          .fontSize(18)
        }
        .justifyContent(FlexAlign.Start)
        .width('100%')
        .height(50)
        .backgroundColor('#fff')


        //结算

        Row({ space: 20 }) {
          Text() {
            Span('共1件，')
              .fontSize(18)
            Span('合计:')
              .fontSize(18)
            Span('￥')
              .fontSize(16)
              .fontColor('#ff4000')
            Span((this.count * this.price + this.freight).toFixed(2))
              .fontSize(22)
              .fontColor('#ff4000')
              .fontWeight(60)
          }
          .margin({ left: 22, bottom: 22 })


          Button('付款')
            .width(122)
            .height(38)
            .backgroundColor('#90EE90')
            .margin({ left: 22, bottom: 22 })
            .onClick(()=>{
              this.maskopacity=1
              this.maskzindex=666
            })
        }
        .margin({ top: 322 })
        .height(88)
        .width('100%')
        .backgroundColor('#ffffff')
      }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
      .width('100%')
      .height('100%')
      .backgroundColor('#F4F8FF')
      //微信支付页面
      Column() {
        Column({space:20}) {
          Row() {
            Image($r('app.media.x'))
              .height(22)
            Text('请输入支付密码')
              .fontSize(16)
              .fontColor('#000000')
              .margin({ left: 99 })
          }
          .padding(15)
          .justifyContent(FlexAlign.Start)
          .width('100%')

          //价格
          Text('生鲜电商平台用户')
            .fontSize(18)

          Text() {
            Span('￥')
              .fontSize(30)
            Span((this.count * this.price + this.freight).toFixed(2))
              .fontSize(40)
          }
          .fontWeight(1200)

          Divider()
            .padding({left:10,right:10})
            .strokeWidth(1)
          //支付方式
          Row({space:10}) {
            Text('支付方式')
              .fontColor('#999')
              .fontSize(16)
            Row() {
              Text('￥')
                .backgroundColor('#FFCC00')
                .height(25)
                .width(25)
                .borderRadius(12.5)
                .fontColor('#ffffff')
                .textAlign(TextAlign.Center)

              Text('零钱')
                .fontColor('#999')
                .fontSize(16)
              Image($r('app.media.right_arrow'))
                .width(20)
                .fillColor('#F2F2F2')
            }
          }
          .padding({left:22,right:22})
          .height(25)
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)

          //密码空格
          Grid() {
            ForEach([1, 2, 3, 4, 5, 6], () => {
              GridItem() {
                Row() {
                }
                .width(42)
                .height(42)
                .borderRadius(5)
                .backgroundColor('#F2F2F2')

              }
            })
          }
          .width('100%')
          .height(45)
          .padding({left:40,right:40})
          .backgroundColor('#ffffff')
          .columnsTemplate('1fr 1fr 1fr 1fr 1fr 1fr')
          .rowsTemplate('1fr')

          //数字轮盘
          Row() {
            Grid() {
              ForEach(this.pswdnum, (num:number) => {
                GridItem() {
                  Row() {
                    Text(num.toString())
                      .fontSize(24)
                      .fontWeight(300)

                  }
                  .justifyContent(FlexAlign.Center)
                  .backgroundColor('#ffffff')
                  .width('100%')
                  .height('100%')
                }
              })
            }
            .width('100%')
            .height(180)
            .rowsGap(2)
            .columnsGap(2)
            .columnsTemplate('1fr 1fr 1fr')
            .rowsTemplate('1fr 1fr 1fr')

          }
          .padding({top:2})
          .alignItems(VerticalAlign.Top)
          .width('100%')
          .height(182)
          .backgroundColor('#f2f2f2')


        }
        .backgroundColor('#ffffff')
        Row(){
          Row(){
            Text(this.pswdnum[9].toString())
              .fontSize(24)
              .fontWeight(300)

          }
          .justifyContent(FlexAlign.Center)
          .height(60)
          .width(128)
          .backgroundColor('#ffffff')
          .margin({top:1,left:130})
          Image($r('app.media.withdraw'))
            .width(30)
            .margin({top:15,left:55})
        }
        .alignItems(VerticalAlign.Top)
        .backgroundColor('#f2f2f2')
        .width('100%')
        .height(80)

      }
      .width('100%')
      .height('100%')
      .backgroundColor('#bb000000')
      .justifyContent(FlexAlign.End)
      .opacity(this.maskopacity)
      .zIndex(this.maskzindex)
      .onClick(()=>{
        router.replaceUrl({
          url:'pages/pay_achievment'})
      })
    }

  }
}