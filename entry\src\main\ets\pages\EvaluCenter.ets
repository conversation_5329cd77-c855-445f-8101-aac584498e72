import { edgeColors, router, Router } from "@kit.ArkUI";
@Entry
@Component
struct EvaluCenter {
  @State message: string = 'Hello World';

  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;
  //定义变量，记录当前选项卡的下标
  @State currentIndex:number = 0

  @Builder
  myItemUI(item:Item1){
    Column({space:20}){
      Row(){
        Text('订单号:'+item.id).fontSize(14)
          .fontColor('#333333');
        if(item.id==='**********'){
          Text(item.date)
            .fontSize(12)
            .fontColor('#999999');
        }


      }.justifyContent(FlexAlign.SpaceBetween)
      .width('100%');

      Row({ space: 12 }){
        Image(item.image)
          .width(60)
          .height(60)
          .borderRadius(8)

          .objectFit(ImageFit.Cover);

        Text(item.name)
          .fontSize(14)
          .fontColor('#333333')
          .maxLines(2)
          .width(140)




        Column({space:30}){
          Text('￥'+item.price).fontSize(12)
            .fontColor('#000000');

          Text('x'+item.count).fontSize(12)
            .fontColor('#999999').padding({left:10});
        }.padding({left:70})

      }

       if(item.id==="**********"){
        Row({space:150}){

          Text('感谢您的用心评价!').height(20).maxLines(1)
            .width(100)
            .fontSize(12)
            .fontColor('#999999');

          Button('评价详细')
            .height(20)
            .width(70)
            .borderRadius(3)
            .backgroundColor('#FFFFFF')
            .fontColor('#000000')
            .borderColor('#999999')
            .borderWidth(1)
            .onClick(() => {
              router.pushUrl({
                url: 'pages/pingjiaxiangqingPage'
              })

            })

        }
      }else if(['**********', '**********'].includes(item.id)){
         Row({space:10}){
           Button('评价晒单')
             .fontColor('#ffffff')
             .backgroundColor('#99CC34')
             .height(20)
             .width(70)
             .borderRadius(3).onClick(() => {
             router.pushUrl({
               url: 'pages/pingjiashaidanPage'
             })

           })
         }.justifyContent(FlexAlign.End)
          .padding({left:250})}




    }.padding(20)
    .width('100%')
    .backgroundColor('#FFFFFF')
    .borderRadius(13)

  }
  datas:Item1[]=[
    {id:'**********',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.11 ',total:60.00},
    {id:'**********',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.11 ',total:60.00},
    {id:'**********',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.12 ',total:60.00},
  ]


  @Builder
  myTabsBar(title:string,index:number){
    Column({space:5}){
      Text(title)
        .fontColor(this.currentIndex == index ? '#99CC34':'#333333')
        .fontSize(16)
      Row()
        .width(20)
        .height(4)
        .backgroundColor(this.currentIndex == index ? '#99CC34':'#FFFFFF')

    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }

  build() {
    Column(){

      Row(){
        Image($r('app.media.u2333'))
          .width(20)
          .height(20)
          .margin({ left: 15, bottom:1 })
          .onClick(() => {
            router.back();
          });

        Text('评价中心')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#FFFFFF')
          .margin({ left: 120, bottom:1 });


        // 右侧按钮组
        Row({ space: 10 }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22);

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24'
        })
        .padding({ left: 20, right: 20, top: 8, bottom: 8 })
        .backgroundColor('#6B8F24')
        .margin({ left:30,right:5,  bottom: 1 });
      }
      .backgroundColor('#99CC33')
      .height(100)
      .padding({top:35,bottom:1})
      .width('100%');

      Row({space:20}){
        Column() {
          // 导航栏 - 使用Tabs组件
          Tabs() {
            TabContent() {
              Column(){
                Scroll(){
                  Column({space:15}){
                    ForEach(this.datas,(item:Item1)=>{

                      if (['**********', '**********'].includes(item.id)) {
                        this.myItemUI(item)

                      }
                    })
                  }
                }.margin({left:10,right:10,top:10})
                .scrollBar(BarState.Off)

              }.height('100%');



            }.tabBar(this.myTabsBar( '待评价', 0)).backgroundColor('#F4F8FF')

            TabContent() {
              Column(){
                ForEach(this.datas,(item:Item1)=>{

                  if (item.id==="**********") {
                    this.myItemUI(item)

                  }

                })
              }.height("100%").margin({left:10,right:10,top:10})
            }.tabBar(this.myTabsBar( '已评价', 1)).backgroundColor('#F4F8FF')




          }
          .onChange(index => {
            this.currentIndex = index;
          })
        }
        .padding({  bottom:0,left:5,right:5 })
        .width('100%')
        .height('100%')

      }.width('100%').backgroundColor('#FFFFFF')







    }.backgroundColor('#F4F8FF')

    .width('100%')
    .padding({bottom:120})
    .height('100%');

  }
}

interface  Item1{
  id:string

  image:Resource
  name:string
  price:number
  count:number
  date:string
  total:number

}