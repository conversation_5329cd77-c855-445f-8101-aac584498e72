import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@CustomDialog
struct MenuDialog {
  controller: CustomDialogController;

  @Builder
  MenuButton(icon: Resource, text: string, onClick?: () => void) {
    Column() {
      Image(icon)
        .width(32)
        .height(32)
        .margin({ bottom: 8 })

      Text(text)
        .fontSize(12)
        .fontColor('#666666')
        .textAlign(TextAlign.Center)
    }
    .width(60)
    .height(70)
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      if (onClick) {
        onClick();
      }
      this.controller.close();
    })
  }

  build() {
    Column() {
      // 顶部标题
      Row() {
        Image($r('app.media.img'))
          .width(24)
          .height(24)
          .borderRadius(12)
          .margin({ right: 8 })

        Text('生鲜电商')
          .fontSize(16)
          .fontColor('#333333')
      }
      .width('100%')
      .padding({ left: 20, top: 20, bottom: 20 })
      .alignItems(VerticalAlign.Center)

      // 第一行菜单
      Row() {
        this.MenuButton($r('app.media.img'), '转发给朋友')
        this.MenuButton($r('app.media.img'), '分享朋友圈')
        this.MenuButton($r('app.media.img'), '添加到我的小程序')
        this.MenuButton($r('app.media.img'), '添加到桌面')
        this.MenuButton($r('app.media.img'), '在电脑上打开')
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
      .margin({ bottom: 20 })

      // 第二行菜单
      Row() {
        this.MenuButton($r('app.media.img'), '设置')
        this.MenuButton($r('app.media.img'), '反馈与投诉')
        this.MenuButton($r('app.media.img'), '重新进入小程序')
        this.MenuButton($r('app.media.img'), '复制链接')
        this.MenuButton($r('app.media.img'), '成长守护防沉迷')
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
      .margin({ bottom: 30 })

      // 取消按钮
      Button('取消')
        .width('90%')
        .height(44)
        .fontSize(16)
        .fontColor('#666666')
        .backgroundColor('#F5F5F5')
        .borderRadius(8)
        .onClick(() => {
          this.controller.close();
        })
        .margin({ bottom: 20 })
    }
    .width('100%')
    .backgroundColor('#FFFFFF')
    .borderRadius({ topLeft: 16, topRight: 16 })
  }
}

@Entry
@Component
struct AAA {
  @State rating: number = 5;
  @State reviewContent: string = '';
  @State isAnonymous: boolean = false;
  private dialogController: CustomDialogController | null = null;

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.fanhui1'))
          .width(24)
          .height(24)
          .onClick(() => {
            router.back();
          })

        Text('评价晒单')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .fontColor('#FFFFFF')
          .margin({left:100})

        //分享和回主页
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:40}); // 调整右边距和顶边距

      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor('#8BC34A')

      // 内容区域
      Column() {
        // 综合评价
        Row() {
          Text('综合评价')
            .fontSize(16)
            .fontColor('#333333')

          Blank()

          // 星级评价
          Row() {
            ForEach([1, 2, 3, 4, 5], (item: number) => {
              Text('★')
                .fontSize(20)
                .fontColor(item <= this.rating ? '#FFD700' : '#E0E0E0')
                .onClick(() => {
                  this.rating = item;
                })
            })
          }

          Text('非常好')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ left: 10 })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 评价内容
        Column() {
          Text('评价内容')
            .fontSize(16)
            .fontColor('#333333')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 10 })

          TextArea({
            placeholder: '请填写10个字以上的评价内容',
            text: this.reviewContent
          })
            .width('100%')
            .height(120)
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .onChange((value: string) => {
              this.reviewContent = value;
            })
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 20 })

        // 商品图片
        Column() {
          Row() {
            Text('商品图片')
              .fontSize(16)
              .fontColor('#333333')

            Text('(0/6)')
              .fontSize(14)
              .fontColor('#666666')
              .margin({ left: 5 })
          }
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 10 })

          // 添加图片按钮
          Column() {
            Text('+')
              .fontSize(30)
              .fontColor('#999999')
          }
          .width(80)
          .height(80)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .justifyContent(FlexAlign.Center)
          .alignSelf(ItemAlign.Start)
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 30 })

        // 匿名评价
        Row() {
          Text('匿名评价')
            .fontSize(16)
            .fontColor('#333333')

          Blank()

          Toggle({ type: ToggleType.Switch, isOn: this.isAnonymous })
            .onChange((isOn: boolean) => {
              this.isAnonymous = isOn;
            })
        }
        .width('100%')
        .margin({ bottom: 40 })

        // 提交按钮
        Button('提交')
          .width('100%')
          .height(48)
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor('#8BC34A')
          .borderRadius(24)
          .onClick(() => {
            promptAction.showToast({
              message: '提交成功！',
              duration: 2000
            });
            setTimeout(() => {
              router.back();
            }, 2000);
          })
      }
      .width('100%')
      .layoutWeight(1)
      .padding(20)
      .backgroundColor('#F8F8F8')
    }
    .width('100%')
    .height('100%')
  }
}