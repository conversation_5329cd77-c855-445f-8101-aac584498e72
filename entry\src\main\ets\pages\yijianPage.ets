import router from '@ohos.router';
@Entry
@Component
struct yijianPage {

  build() {
    //最外层
    Column({space:10}) {
      //顶部栏
      Row() {

        //返回键

        Image($r('app.media.fanhui1'))

          .width(20)
          .height(20)
          .onClick(() => {
            router.back()
          })


        Text('意见反馈')
          .fontSize(25)
          .fontColor('#ffffff')
          .margin({left:100})

        //.margin({right:100})
        //分享和回主页
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:50}); // 调整右边距和顶边距

      }
      .backgroundColor('#99CC33')
      .width('100%')
      .height(80)
      .borderRadius(8)
      .padding(10)

      //反馈内容
      Column({space:8}){
        Text('反馈内容')
          .fontSize(20)
          .margin({left:10})


        TextInput({placeholder:'请填写10个字以上的内容'})
          .height(150)
          .width(360)
          .backgroundColor('#F2F2F2')
          .padding({left:6,top:8})




      }
      .backgroundColor('#FFFFFF')
      .width(370)
      .height(200)
      .margin({left:10,right:10})
      .alignItems(HorizontalAlign.Start)
      .borderRadius(20)

      //相关jietu
      Column({space:8}){
        Text('相关截图 （0/3）')
          .fontSize(20)
          .padding({left:10})


        Image($r('app.media.tianjiatupian'))
          .height(120)
          .width(120)
          .padding(10)
          .backgroundColor('#F2F2F2')

      }
      .backgroundColor('#FFFFFF')
      .alignItems(HorizontalAlign.Start)
      //.justifyContent(FlexAlign.SpaceBetween)
      .width(370)
      .height(180)
      .margin({left:10,right:10})
      .borderRadius(20)

      Column(){
        Row(){
        Text('联系方式')
          .fontSize(20)
          TextInput({placeholder:'邮箱/手机号码'})
            .fontColor('#9F9A99')
            .backgroundColor('#ffffff')
            .width(360)
            .layoutWeight(1)
        }
      }
      .width(370)
      .height(60)
      .backgroundColor('#ffffff')
      .borderRadius(20)
      .padding({top:10,left:10})

      Button('提交')
        .width(370)
        .height(40)
        .borderRadius(20)
        .backgroundColor('#99CC33')
        .margin({top:40})


    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F4F8FF')
  }
}