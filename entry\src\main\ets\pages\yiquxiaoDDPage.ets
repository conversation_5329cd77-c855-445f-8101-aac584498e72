import router from '@ohos.router';

@Entry
@Component
struct Wait_rec {
  @State orderId: string = '**********';
  @State orderTime: string = '2024.12.12 12:12';
  @State invoiceInfo: string = '专用发票';
  @State paymentTime: string = '2024.12.12 12:21';
  @State paymentMethod: string = '微信支付';
  @State count: number = 1
  @State freight: number = 15
  @State price: number = 60
  @State message: string = 'Hello World';

  build() {
    Column() {
      Row() {
        //返回键

        Image($r("app.media.fanhui1"))
          .width(20)
          .margin({ left: 10, top: 5 })
          .fillColor('#ffffff')
          .onClick(() => {
            router.back()
          })
        Text('已取消')
          .fontColor('#ffffff')
          .margin({ left: 80, top: 5 })
          .fontSize(18)
          .fontWeight(88)
        Row({
          space: 10
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({
          left: 20,
          right: 20,
          top: 8,
          bottom: 8
        })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({ left: 22, right: 10, top: 5 });

      }
      .justifyContent(FlexAlign.SpaceBetween)
      .width('100%')
      .padding({ left: 5, right: 5 })
      .height(60)
      .backgroundColor('#99CC33')
      //发货时间
      Text('取消原因:订单超时')
        .backgroundColor('#99CC33')
        .width('100%')
        .fontColor('#ffffff')
        .textAlign(TextAlign.Center)
        .height(40)
      Row(){
        //左侧列
        Column({space: 12}) {
          Text('湖北省荆州市学苑路汉科10086栋')
            .fontSize(14)
            .fontWeight(700)
            .margin({top:20,left:10})
          Text('xun先生 153****1234')
            .fontSize(14)
            .fontWeight(700)
            .fontColor('#999')
            .margin({bottom:20,left:10})
        }
        .alignItems(HorizontalAlign.Start)
      }
      .margin({top:10})
      .padding(10)
      .borderRadius(10)
      .justifyContent(FlexAlign.Start)
      .width(350)
      .height(100)
      .backgroundColor('#fff')

      //商品
      Row() {
        Image($r('app.media.apple3'))
          .width(80)
          .borderRadius(8)
          .margin({ left: 5 })
        Text('纯天然大苹果')
          .lineHeight(20)
          .fontSize(18)

        Column() {
          Text() {
            Span('￥')
              .fontSize(16)
            Span(this.price.toFixed(2))
              .fontSize(16)
          }

          Text('x1')
            .fontColor('#9CA6C4')
            .width(22)
            .height(22)
            .textAlign(TextAlign.Center)
            .fontWeight(700)
            .margin(22)

        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.End)
        .margin({top:10,right:10})
      }
      .borderRadius(10)
      .justifyContent(FlexAlign.Start)
      .width(350)
      .height(100)
      .backgroundColor('#fff')
      .margin(10)

      //价格
      Column() {
        Row() {
          Text('订单金额')
            .fontColor('#9CA6C4')
            .fontSize(14)
            .margin({ left: 10 })
          Text('￥60.00')
            .fontSize(14)
            .textAlign(TextAlign.End)
            .layoutWeight(1)
            .fontSize(14)
        }
        .justifyContent(FlexAlign.Start)
        .width('100%')
        .height(40)
        .backgroundColor('#fff')

        Row() {
          Text('订单运费')
            .fontColor('#9CA6C4')
            .fontSize(14)
            .margin({ left: 10 })
          Text() {
            Span('￥')
              .fontSize(14)
            Span(this.freight.toFixed(2))
              .fontSize(14)
            Span('(顺丰快递)')
              .fontSize(14)

          }
          .textAlign(TextAlign.End)
          .layoutWeight(1)
        }
        .justifyContent(FlexAlign.SpaceBetween)
        .width('100%')
        .height(40)
        .backgroundColor('#fff')

        Divider()
          .color('#F2F1F2')
          .width(340)
          .strokeWidth(1)

        Row() {
          Text ('金额:')
            .fontColor('#9CA6C4')
            .fontSize(14)
            .margin({ left: 10 })
          Text() {
            Span('￥')
              .fontSize(14)
              .fontColor('#ff4000')

            Span((this.count * this.price + this.freight).toFixed(2))
              .fontSize(14)
              .fontColor('#ff4000')
              .fontWeight(60)
          }
          .textAlign(TextAlign.End)
          .layoutWeight(1)
        }
        .width('100%')
        .height(40)
        .backgroundColor('#fff')
      }
      .borderRadius(10)
      .width(350)

      //订单信息
      Column({ space: 20 }){
        Row({ space: 10 }) {
          Text('订单编号：')
            .fontColor('#9CA6C4')
            .fontSize(14);
          Row() {
            Text(this.orderId)
              .fontSize(14)
              .textAlign(TextAlign.End)


            Button('复制')
              .onClick(() => this.copyOrderId())
              .width(50)
              .height(20)
              .borderRadius(10)
              .backgroundColor('#F2F2F2')
              .fontColor('#99CC33')
              .margin({ left: 5 });
          }
          .justifyContent(FlexAlign.End)
          .layoutWeight(1)
        }

        // 下单时间行
        Row({ space: 10 }) {
          Text('下单时间：')
            .fontColor('#9CA6C4')
            .fontSize(14);

          Text(this.orderTime)
            .fontSize(14)
            .textAlign(TextAlign.End)
            .layoutWeight(1)
        }

        // 发票信息行
        Row({ space: 10 }) {
          Text('发票信息：')
            .fontColor('#9CA6C4')
            .fontSize(14);

          Text(this.invoiceInfo)
            .fontSize(14)
            .textAlign(TextAlign.End)
            .layoutWeight(1)
        }
        .justifyContent(FlexAlign.SpaceBetween)


      }
      .alignItems(HorizontalAlign.Start)
      .width(350)
      .padding(10)
      .borderRadius(10)
      .backgroundColor('#fff')
      .margin({ top: 10 })
      //收获按钮
      Row(){
        Button('删除订单')
          .width(100)
          .height(40)
          .borderRadius(10)
          .backgroundColor('#ffffff')
          .fontColor('#000000')
        //  .margin({ top: 100 })
      }
      .backgroundColor('#ffffff')
      .justifyContent(FlexAlign.End)
      .width('100%')
      .height(50)
      .margin({top:150,right:20})
    }
    .backgroundColor('#F4F8FF')
  }

  copyOrderId() {
    console.log('复制订单编号：' + this.orderId);
    // 实现复制逻辑
  }
}
