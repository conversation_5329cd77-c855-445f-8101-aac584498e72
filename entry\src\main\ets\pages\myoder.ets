import { edgeColors, router, Router } from "@kit.ArkUI";

@Entry
@Component
export struct  Myoder {
  @State message: string = 'Hello World';
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;
  //定义变量，记录当前选项卡的下标
  @State currentIndex:number = 0
//UI
  @Builder
  myItemUI(item:Item1){
    Column({space:20}){
      Row(){
        Text('订单号:'+item.id).fontSize(14)
          .fontColor('#333333');
        Text(item.status).fontSize(14)
          .fontColor('#FF0000') // 红色标记状态
          .fontWeight(FontWeight.Bold);

      }.justifyContent(FlexAlign.SpaceBetween)
      .width('100%');

      Row({ space: 12 }){
        Image(item.image)
          .width(60)
          .height(60)
          .borderRadius(8)

          .objectFit(ImageFit.Cover);

        Text(item.name)
          .fontSize(14)
          .fontColor('#333333')
          .maxLines(2)
          .width(140)




        Column({space:30}){
          Text('￥'+item.price).fontSize(12)
            .fontColor('#000000');

          Text('x'+item.count).fontSize(12)
            .fontColor('#999999').padding({left:10});
        }.padding({left:70})

      }
      Row({space:90}){
        Text(item.date)
          .fontSize(12)
          .fontColor('#999999');

        Text('金额：￥'+item.total)
          .fontSize(12)
          .fontColor('#333333')
          .flexGrow(1)
          .padding({left:70})
      }
      if (item.status==="待付款"){
        Row({space:10}){
          Button('取消订单')
            .height(20)
            .width(70)
            .borderRadius(3)
            .backgroundColor('#FFFFFF')
            .fontColor('#000000')
            .borderColor('#999999')
            .borderWidth(1)

          Button('立即付款')
            .fontColor('#ffffff')
            .backgroundColor('#99CC34')
            .height(20)
            .width(70)
            .borderRadius(3)


        }.justifyContent(FlexAlign.End).padding({left:170})

      }else if(item.status==="待收货"){
        Row({space:10}){


          Button('确认收货')
            .fontColor('#ffffff')
            .backgroundColor('#99CC34')
            .height(20)
            .width(70)
            .borderRadius(3)


        }.justifyContent(FlexAlign.End).padding({left:250})
      }else if(item.status==="已完成"){
        Row({space:10}){
          Button('删除订单')
            .height(20)
            .width(70)
            .borderRadius(3)
            .backgroundColor('#FFFFFF')
            .fontColor('#000000')
            .borderColor('#999999')
            .borderWidth(1)

        }.justifyContent(FlexAlign.End).padding({left:250})

      }else if(item.status==="已取消"){
        Row({space:10}){
          Button('删除订单')
            .height(20)
            .width(70)
            .borderRadius(3)
            .backgroundColor('#FFFFFF')
            .fontColor('#000000')
            .borderColor('#999999')
            .borderWidth(1)
        }.justifyContent(FlexAlign.End).padding({left:250})
      }

    }.padding(20)
    .width('100%')
    .backgroundColor('#FFFFFF')
    .borderRadius(13)



  }


//数据
  datas:Item1[]=[
    {id:'**********',status:'待付款',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.12 12:12',total:60.00},
    {id:'**********',status:'待收货',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.12 12:12',total:60.00},
    {id:'**********',status:'已完成',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.12 12:12',total:60.00},
    {id:'**********',status:'已取消',image:$r('app.media.lp'),name:'纯天然大苹果果来自大自然的天然产物',price:20.00,count:3,date:'2024.12.12 12:12',total:60.00}
  ]




  //定义变量用标识显示最新还是最热
  @State isShow:boolean = true
  @Builder
  myTabsBar(title:string,index:number){
    Column({space:5}){
      Text(title)
        .fontColor(this.currentIndex == index ? '#99CC34':'#333333')
        .fontSize(16)
      Row()
        .width(20)
        .height(4)
        .backgroundColor(this.currentIndex == index ? '#99CC34':'#FFFFFF')

    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }



  build() {
    Column(){

      Row(){
        Image($r('app.media.u2333'))
          .width(20)
          .height(20)
          .margin({ left: 15, bottom:1 })
          .onClick(() => {
            router.back();
          });

        Text('我的订单')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#FFFFFF')
          .margin({ left: 120, bottom:1 });


        // 右侧按钮组
        Row({ space: 10 }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22);

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24'
        })
        .padding({ left: 20, right: 20, top: 8, bottom: 8 })
        .backgroundColor('#6B8F24')
        .margin({ left:30,right:5,  bottom: 1 });
      }
      .backgroundColor('#99CC33')
      .height(100)
      .padding({top:35,bottom:1})
      .width('100%');

      Row({space:20}){
        Column() {
          // 导航栏 - 使用Tabs组件
          Tabs() {
            TabContent() {
              Scroll(){
                Column({space:15}){
                  ForEach(this.datas,(item:Item1)=>{

                    this.myItemUI(item)
                  })
                }
              }.margin({left:10,right:10,top:10})
              .scrollBar(BarState.Off)
              .height('100%')

            }.tabBar(this.myTabsBar( '全部', 0)).backgroundColor('#F4F8FF')

            TabContent() {
              Column(){
                ForEach(this.datas,(item:Item1)=>{

                  if (item.status==="待付款") {
                    this.myItemUI(item)

                  }

                })
              }.height("100%").margin({left:10,right:10,top:10})
            }.tabBar(this.myTabsBar( '待付款', 1)).backgroundColor('#F4F8FF')

            TabContent() {
              Column(){
                ForEach(this.datas,(item:Item1)=>{

                  if (item.status==="待收货") {
                    this.myItemUI(item)

                  }

                })
              }.height("100%").margin({left:10,right:10,top:10})
            }.tabBar(this.myTabsBar( '待收货', 2)).backgroundColor('#F4F8FF')

            TabContent() {
              Column(){
                ForEach(this.datas,(item:Item1)=>{

                  if (item.status==="已完成") {
                    this.myItemUI(item)

                  }

                })
              }.height("100%").margin({left:10,right:10,top:10})
            }.tabBar(this.myTabsBar( '已完成', 3)).backgroundColor('#F4F8FF')

            TabContent() {
              Column(){
                ForEach(this.datas,(item:Item1)=>{

                  if (item.status==="已取消") {
                    this.myItemUI(item)

                  }

                })
              }.height("100%").margin({left:10,right:10,top:10})
            }.tabBar(this.myTabsBar( '已取消', 4)).backgroundColor('#F4F8FF')
          }
          .onChange(index => {
            this.currentIndex = index;
          })
        }
        .padding({  bottom:0,left:5,right:5 })
        .width('100%')
        .height('100%')

      }.width('100%').backgroundColor('#FFFFFF')







    }.backgroundColor('#F4F8FF')

    .width('100%')
    .padding({bottom:120})
    .height('100%');

    }


}


interface  Item1{
  id:string
  status:string
  image:Resource
  name:string
  price:number
  count:number
  date:string
  total:number

}



