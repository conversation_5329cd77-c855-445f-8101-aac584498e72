import router from '@ohos.router';
interface  Item2{
  img:Resource
  name:string
  dislike:string
  price:string
  sellnum:string

}
@Entry
@Component
struct zzMylove {
  //数据，模拟数据未来后端提供
  datas:Item2[ ]=[

    {img:$r('app.media.apple1'),name:'湖北红苹果',dislike:'取消收藏',price:'￥21.5元',sellnum:'已售100万斤'},
    {img:$r('app.media.apple2'),name:'湖南红苹果',dislike:'取消收藏',price:'￥32.5元',sellnum:'已售100万斤'},
    {img:$r('app.media.apple3'),name:'美国红苹果',dislike:'取消收藏',price:'￥1.5元',sellnum:'已售100万斤'},
    {img:$r('app.media.apple4'),name:'日本红苹果',dislike:'取消收藏',price:'￥2.35元',sellnum:'已售100万斤'},
    {img:$r('app.media.apple4'),name:'非洲红苹果',dislike:'取消收藏',price:'￥2.85元',sellnum:'已售100万斤'},
   // {img:$r('app.media.apple5'),name:'东北红苹果',dislike:'取消收藏',price:'￥22.5元',sellnum:'已售100万斤'},
  ]

  //UI
  @Builder
  myItemUI(item:Item2) {

    Row({space:10}) {
      Image(item.img)
        .width(80)
        .height(80)
        .borderRadius(12)

      Column({space:15}) {
        Text(item.name)
          .fontSize(20)
          .fontWeight(200)



        Row({space:30}) {

          Text(item.price)
            .fontSize(20)
            .fontWeight(300)
            .fontColor('#fff5072b')

          Text(item.sellnum)
            .fontSize(10)
            .fontWeight(100)

          Text(item.dislike)
            .fontSize(13)
            .fontWeight(100)
            .borderColor('#ff030303')
            .margin({right:10})
        }
        .margin({left:10})

      }
    }
    .width(350)
    .height(100)
    .borderRadius(15)
    .backgroundColor('#ffe8fcdf')
    .margin({ top: 20 })


  }


  build() {
    //最外层
    Column() {
      //顶部搜索栏
      Row() {

        //返回键

        Image($r('app.media.fanhui1'))

          .width(20)
          .height(20)
          .onClick(() => {
            router.back()
          })
        /*  .onClick(() => {
            router.pushUrl({
              url: 'pages/Index'
            })
          })
*/

        Text('我的收藏')
          .fontSize(25)
          .fontColor('#ffffff')
          .margin({left:100})

        //.margin({right:100})
        //分享和回主页
        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:40}); // 调整右边距和顶边距

      }
      .backgroundColor('#99CC33')
      .width('100%')
      .height(80)
      .borderRadius(8)
      .padding(10)

      //各个地址
      Scroll(){

        Column({space:5}){

          //循环遍历数据渲染列表
          //参数1 表示数据  参数2 遍历的结果
          ForEach(this.datas,(item:Item2)=>{
            this.myItemUI(item)

          })

        }
        .margin({left:20})
        .onClick(() => {
          router.pushUrl({
            url: 'pages/yishangpinPage'
          })
        })



      }.scrollable(ScrollDirection.Vertical)

    }
  }
}