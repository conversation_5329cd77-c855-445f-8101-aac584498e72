import { home } from './home';
import  { ShoppingCart } from './ShoppingCart'
import { fenlei } from './fenlei';
import { wodenew } from './wodenew';

@Entry
@Component
struct Index {
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  //定义变量，记录当前选项卡的下标
  @State currentIndex:number = 0

  @Builder
  myTabsBar(img:Resource,title:string,index:number){
    Column({space:5}){
      Image(img)
        .fillColor(this.currentIndex == index ? '#99CC34':'#333333')//填充色
        .width(20)
        .height(20)
      Text(title)
        .fontColor(this.currentIndex == index ? '#99CC34':'#333333')
        .fontSize(16)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }

  build() {
    Column() {



      // 底部导航栏 - 使用Tabs组件，barPosition设置为BarPosition.End
      Tabs({ barPosition: BarPosition.End }) {
        TabContent() {
          home({jumpTofenlei:()=>this.currentIndex=1})
        }.tabBar(this.myTabsBar($r('app.media.u1805'), '首页', 0))

        TabContent() {
          fenlei()
        }.tabBar(this.myTabsBar($r('app.media.u1809'), '分类', 1))

        TabContent() {
          ShoppingCart()
        }.tabBar(this.myTabsBar($r('app.media.u1813'), '购物车', 2))

        TabContent() {
          wodenew()
        }.tabBar(this.myTabsBar($r('app.media.u1817'), '我的', 3))
      }
      .onChange(index => {
        this.currentIndex = index;
      })
    }
    .padding({  bottom: px2vp(this.bottomRectHeight)+10,left:0,right:0 })


    .width('100%')
    .height('100%')
  }

}