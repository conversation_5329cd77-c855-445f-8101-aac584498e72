import router from '@ohos.router';
@Component
export struct wodenew {
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;


  build() {
    //页面
    Column({space:15}){
    //顶部用户信息
      Row(){
        Image($r('app.media.a'))
          .width(40)
          .height(40)
          .borderRadius(15)
        Column(){
          Text('userName')
          Text('13456789000')
            .onClick(() => {


              router.pushUrl({
                url: 'pages/ziliao'
              })

            })
        }

        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(15)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(14)

          Image($r('app.media.exitcircle'))
            .width(15)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:100}); // 调整右边距和顶边距

      }
      .backgroundColor('#99CC33')
      .width('100%')
      .height(80)
      .padding(10)

      //我的订单部分
      Column({space:15}) {
        Row({space:200}) {
          Text('我的订单')
            .fontSize(18)
            .fontColor('#333')

          Text('查看')
            .fontSize(14)
            .fontColor('#999')

            }.onClick(() => {


          router.pushUrl({
            url: 'pages/myoder'
          })

        })
            //四个图标
        Row({space:35}){
          Column({space:15}){
            Image($r('app.media.zzzdaifukuan'))
              .width(40)
              .height(40)
              .borderRadius(8)
            Text('待付款')
          }

          Column({space:15}){
            Image($r('app.media.zzzdaishouhuo'))
              .width(40)
              .height(40)
              .borderRadius(8)
            Text('待收货')
          }

          Column({space:15}){
            Image($r('app.media.zzzyiwancheng'))
              .width(40)
              .height(40)
              .borderRadius(8)
            Text('已完成')
          }

          Column({space:15}){
            Image($r('app.media.zzzdaipingjia'))
              .width(40)
              .height(40)
              .borderRadius(8)
            Text('待评价')
          }.onClick(() => {


            router.pushUrl({
              url: 'pages/EvaluCenter'
            })

          })
        }
        .height(140)

        }
        .padding({ left: 15, right: 15, top: 10, bottom: 10 })
        .width('100%')
      .borderRadius(15)
      .backgroundColor('#ffffff')

        //中间四个选项
       Column({space:25}){
        Row({space:15}){
          Image($r('app.media.zzzdizhi'))
            .width(15)
            .height(15)
          Text('我的地址')
        }.onClick(() => {
          router.pushUrl({
            url: 'pages/Myaddress'
          })

        })
        .width('100%')

         Row({space:15}){
           Image($r('app.media.zzzshoucang'))
             .width(15)
             .height(15)
           Text('我的收藏')
         }
         .width('100%')
         .onClick(() => {
           router.pushUrl({
             url: 'pages/zzzMylikePage'
           })
         })

         Row({space:15}){
           Image($r('app.media.zzzkefu'))
             .width(15)
             .height(15)
           Text('联系客服')
         }
         .width('100%')
         .onClick(() => {
           router.pushUrl({
             url: 'pages/zzzcallkefuPage'
           })
         })

         Row({space:15}){
           Image($r('app.media.zzzyijainfankui'))
             .width(15)
             .height(15)
           Text('意见反馈')
         }
         .width('100%')
         .onClick(() => {
           router.pushUrl({
             url: 'pages/yijianPage'
           })
         })

       }
       .padding({ left: 15, right: 15, top: 10, bottom: 10 })
       .width('100%')
       .borderRadius(15)
       .backgroundColor('#ffffff')

      //有奖活动
      Image($r('app.media.zzzyoujiangyaoqing'))
        .width('100%')
        .borderRadius(15)
        .onClick(() => {
          router.pushUrl({
            url: 'pages/huodongPage'
          })
        })

      //退出
      Text('退出')
        .height(30)


    }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .width('100%')
    .height('100%')
    .backgroundColor('#fffaf5f5')
  }
}