import router from '@ohos.router';

interface FriendInfo {
  phone: string;
  date: string;
}

@Entry
@Component
struct InvitePage {
  @State friends: Array<FriendInfo> = [
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"},
    {phone: "180***0626", date: "2024.12.12 12:12"}
  ]
  @State currentTab: number = 0
  @State showRulesDialog: boolean = false // 控制活动规则弹窗显示
  @State showShareDialog: boolean = false // 控制分享弹窗显示
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  build() {
    Stack() {
      Column() {
        // 顶部导航栏
        Row() {
          Image($r('app.media.fanhui1'))
            .width(24)
            .height(24)
            .margin({ left: 16 })
            .onClick(() => {
              router.pushUrl({
                url: 'pages/Index'
              })
            })
            /*.onClick(() => {
              router.back()
            })*/
            
          Text('邀请好友领生鲜')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
            
          Row({ space: 8 }) {
            Image($r('app.media.fenxiang'))
              .width(24)
              .height(24)
              
            Image($r('app.media.huizhuye'))
              .width(24)
              .height(24)
          }
          .margin({ right: 16 })
        }
        .width('100%')
        .height(56)
        .backgroundColor(Color.White)
        
        // 主内容区域（可滚动）
        Scroll() {
          Column() {
            // 主要内容区域
            Stack({ alignContent: Alignment.Top }) {
              // 背景图
              Image($r('app.media.huodongbeij'))
                .width('100%')
                .height(700)
                .objectFit(ImageFit.Cover)
              
              Column() {
                // 活动规则按钮
                Text('活动规则')
                  .fontSize(14)
                  .fontColor(Color.White)
                  .backgroundColor('#FF6B3B')
                  .borderRadius(16)
                  .padding({ left: 12, right: 12, top: 4, bottom: 4 })
                  .position({ x: '85%', y: 20 })
                  .onClick(() => {
                    this.showRulesDialog = true
                  })
                
                // 邀请好友领生鲜标题
                Text('邀请好友领生鲜')
                  .fontSize(28)
                  .fontColor(Color.White)
                  .fontWeight(FontWeight.Bold)
                  .margin({ top: 60 })
                
                // 邀请卡片
                Column() {
                  // 橙色卡片区域
                  Column() {
                    // 邀请2位好友注册
                    Row() {
                      Text('邀请')
                        .fontSize(20)
                        .fontColor(Color.White)
                      
                      Text('2')
                        .fontSize(20)
                        .fontColor('#FFEB3B')
                        .fontWeight(FontWeight.Bold)
                      
                      Text('位好友注册')
                        .fontSize(20)
                        .fontColor(Color.White)
                    }
                    .margin({ top: 40 })
                    
                    Divider()
                      .width('90%')
                      .color(Color.White)
                      .opacity(0.3)
                      .margin({ top: 8, bottom: 8 })
                    
                    // 立得6斤
                    Row() {
                      Text('立得')
                        .fontSize(24)
                        .fontColor(Color.White)
                      
                      Text('6')
                        .fontSize(40)
                        .fontColor('#FFEB3B')
                        .fontWeight(FontWeight.Bold)
                      
                      Text('斤')
                        .fontSize(24)
                        .fontColor(Color.White)
                      
                      Text('有机蔬菜')
                        .fontSize(14)
                        .fontColor(Color.White)
                        .backgroundColor('#4CAF50')
                        .borderRadius(16)
                        .padding({ left: 10, right: 10, top: 3, bottom: 3 })
                        .margin({ left: 10 })
                    }
                    .margin({ top: 8 })
                    .alignItems(VerticalAlign.Bottom)
                    
                    // 最高可得说明
                    Text('最高可有机会获得价值100元的蔬菜')
                      .fontSize(14)
                      .fontColor(Color.White)
                      .opacity(0.8)
                      .margin({ top: 16, bottom: 20 })
                  }
                  .width('100%')
                  .backgroundColor('#FF7043')
                  .borderRadius({ topLeft: 8, topRight: 8 })
                  .alignItems(HorizontalAlign.Center)
                  
                  // 好友可得区域
                  Row() {
                    Text('好友可得')
                      .fontSize(14)
                      .fontColor(Color.White)
                      .backgroundColor('#FF7043')
                      .padding({ left: 12, right: 12, top: 6, bottom: 6 })
                    
                    Text('被邀请人可获得1斤蔬菜')
                      .fontSize(14)
                      .fontColor('#FF7043')
                      .backgroundColor('#FFF3E0')
                      .padding({ left: 12, right: 12, top: 6, bottom: 6 })
                      .layoutWeight(1)
                  }
                  .width('100%')
                  
                  // 立即邀请按钮
                  Button('立即邀请')
                    .width('90%')
                    .height(50)
                    .fontSize(18)
                    .fontWeight(FontWeight.Medium)
                    .backgroundColor('#FF7043')
                    .borderRadius(25)
                    .margin({ top: 20, bottom: 20 })
                    .onClick(() => {
                      this.showShareDialog = true
                    })
                }
                .width('85%')
                .backgroundColor(Color.White)
                .borderRadius(16)
                .margin({ top: 20 })
                .alignItems(HorizontalAlign.Center)
              }
              .width('100%')
              .height('100%')
              .alignItems(HorizontalAlign.Center)
            }
            
            // 统计区域
            Row() {
              Column() {
                Text('300')
                  .fontSize(24)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Bold)
                Text('累计获得(斤)')
                  .fontSize(14)
                  .fontColor('#666666')
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Center)
              
              Divider()
                .vertical(true)
                .height(40)
                .color('#EEEEEE')
              
              Column() {
                Text('10')
                  .fontSize(24)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Bold)
                Text('成功邀请(人)')
                  .fontSize(14)
                  .fontColor('#666666')
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Center)
            }
            .width('100%')
            .height(100)
            .backgroundColor(Color.White)
            .padding({ top: 16, bottom: 16 })

            
            // 选项卡
            Column() {
              // 选项卡标题
              Row() {
                Column() {
                  Text('我的邀请')
                    .fontSize(16)
                    .fontColor(this.currentTab === 0 ? '#FF7043' : '#999999')
                    .fontWeight(this.currentTab === 0 ? FontWeight.Medium : FontWeight.Normal)
                    .onClick(() => {
                      this.currentTab = 0
                    })
                  
                  Divider()
                    .width(20)
                    .height(2)
                    .color('#FF7043')
                    .opacity(this.currentTab === 0 ? 1 : 0)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Center)
                
                Column() {
                  Text('邀请排行榜')
                    .fontSize(16)
                    .fontColor(this.currentTab === 1 ? '#FF7043' : '#999999')
                    .fontWeight(this.currentTab === 1 ? FontWeight.Medium : FontWeight.Normal)
                    .onClick(() => {
                      this.currentTab = 1
                    })
                  
                  Divider()
                    .width(20)
                    .height(2)
                    .color('#FF7043')
                    .opacity(this.currentTab === 1 ? 1 : 0)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Center)
              }
              .width('100%')
              .padding({ top: 15, bottom: 15 })
              
              // 分隔线
              Divider()
                .width('100%')
                .color('#EEEEEE')
              
              // 我邀请的好友
              Text('我邀请的好友')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .width('100%')
                .padding({ left: 16, top: 16, bottom: 8 })
              
              // 表头
              Row() {
                Text('主动邀请')
                  .fontSize(14)
                  .fontColor('#999999')
                  .layoutWeight(1)
                  .textAlign(TextAlign.Center)
                
                Text('注册时间')
                  .fontSize(14)
                  .fontColor('#999999')
                  .layoutWeight(1)
                  .textAlign(TextAlign.Center)
              }
              .width('100%')
              .padding({ top: 8, bottom: 8 })
              
              // 好友列表
              ForEach(this.friends, (friend: FriendInfo) => {
                Row() {
                  Text(friend.phone)
                    .fontSize(14)
                    .fontColor('#333333')
                    .layoutWeight(1)
                    .textAlign(TextAlign.Center)
                  
                  Text(friend.date)
                    .fontSize(14)
                    .fontColor('#333333')
                    .layoutWeight(1)
                    .textAlign(TextAlign.Center)
                }
                .width('100%')
                .height(50)
                .backgroundColor(Color.White)
              })
              
              // 底部提示
              Row() {
                Text('一共有10位好友被你邀请成功')
                  .fontSize(14)
                  .fontColor('#999999')
                Image($r('app.media.pot'))
                  .width(16)
                  .height(16)
                  .fillColor('#999999')
                  .margin({ left: 4 })
              }
              .width('100%')
              .justifyContent(FlexAlign.Center)
              .padding({ top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .width('100%')
          }
        }
        .layoutWeight(1)
      }
      .width('100%')
      .height('100%')
      
      // 活动规则弹窗
      if (this.showRulesDialog) {
        Column() {
          // 半透明背景
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('#000000')
            .opacity(0.5)
            .onClick(() => {
              this.showRulesDialog = false
            })
        }
        .width('100%')
        .height('100%')
        .position({ x: 0, y: 0 })
        
        // 弹窗内容
        Column() {
          // 标题
          Row() {
            Divider().width(80).height(1).color('#DDDDDD')
            Text('活动规则')
              .fontSize(16)
              .fontColor('#333333')
              .margin({ left: 16, right: 16 })
            Divider().width(80).height(1).color('#DDDDDD')
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          .margin({ top: 20, bottom: 20 })
          
          // 规则内容
          Scroll() {
            Column({ space: 16 }) {
              Text('1. 活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明。')
                .fontSize(14)
                .fontColor('#666666')
                .width('100%')
              
              Text('2. 活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明。')
                .fontSize(14)
                .fontColor('#666666')
                .width('100%')
              
              Text('3. 活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明。')
                .fontSize(14)
                .fontColor('#666666')
                .width('100%')
              
              Text('4. 活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明活动规则说明。')
                .fontSize(14)
                .fontColor('#666666')
                .width('100%')
            }
            .width('100%')
            .padding({ left: 20, right: 20 })
          }
          .width('100%')
          .height(300)
          .margin({ bottom: 20 })
          
          // 关闭按钮
          Button('关闭')
            .width(120)
            .height(40)
            .fontSize(16)
            .fontColor(Color.White)
            .backgroundColor('#FF7043')
            .borderRadius(20)
            .margin({ bottom: 20 })
            .onClick(() => {
              this.showRulesDialog = false
            })
        }
        .width('90%')
        .backgroundColor(Color.White)
        .borderRadius(16)
        .position({ x: '5%', y: '15%' })
        .alignItems(HorizontalAlign.Center)
      }
      // 分享弹窗
      if (this.showShareDialog) {
        Column() {
          // 半透明背景
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('#000000')
            .opacity(0.5)
            .onClick(() => {
              this.showShareDialog = false
            })
        }
        .width('100%')
        .height('100%')
        .position({ x: 0, y: 0 })
        
        // 弹窗内容
        Column() {
          // 关闭按钮
          Row() {
            Blank()
            Image($r('app.media.chacha'))
              .width(24)
              .height(24)
              .margin({ right: 16, top: 16 })
              .onClick(() => {
                this.showShareDialog = false
              })
          }
          .width('100%')
          
          // LOGO和标题
          Row() {
            Text('LOGO')
              .fontSize(14)
              .fontColor(Color.White)
              .width(40)
              .height(40)
              .borderRadius(20)
              .backgroundColor('#8BC34A')
              .textAlign(TextAlign.Center)
              .margin({ right: 8 })
            
            Text('生鲜电商')
              .fontSize(18)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .padding({ left: 20, top: 10 })
          .alignItems(VerticalAlign.Center)
          
          // 邀请卡片
          Column() {
            // 橙色卡片区域
            Column() {
              // 邀请2位好友注册
              Row() {
                Text('邀请')
                  .fontSize(20)
                  .fontColor(Color.White)
                
                Text('2')
                  .fontSize(20)
                  .fontColor('#FFEB3B')
                  .fontWeight(FontWeight.Bold)
                
                Text('位好友注册')
                  .fontSize(20)
                  .fontColor(Color.White)
              }
              .margin({ top: 20 })
              
              Divider()
                .width('90%')
                .color(Color.White)
                .opacity(0.3)
                .margin({ top: 8, bottom: 8 })
              
              // 立得6斤
              Row() {
                Text('立得')
                  .fontSize(24)
                  .fontColor(Color.White)
                
                Text('6')
                  .fontSize(40)
                  .fontColor('#FFEB3B')
                  .fontWeight(FontWeight.Bold)
                
                Text('斤')
                  .fontSize(24)
                  .fontColor(Color.White)
                
                Text('有机蔬菜')
                  .fontSize(14)
                  .fontColor(Color.White)
                  .backgroundColor('#4CAF50')
                  .borderRadius(16)
                  .padding({ left: 10, right: 10, top: 3, bottom: 3 })
                  .margin({ left: 10 })
              }
              .margin({ top: 8 })
              .alignItems(VerticalAlign.Bottom)
              
              // 最高可得说明
              Text('最高可有机会获得价值100元的蔬菜')
                .fontSize(14)
                .fontColor(Color.White)
                .opacity(0.8)
                .margin({ top: 16, bottom: 20 })
            }
            .width('100%')
            .backgroundColor('#FF7043')
            .borderRadius({ topLeft: 8, topRight: 8 })
            .alignItems(HorizontalAlign.Center)
            
            // 好友可得区域
            Row() {
              Text('好友可得')
                .fontSize(14)
                .fontColor(Color.White)
                .backgroundColor('#FF7043')
                .padding({ left: 12, right: 12, top: 6, bottom: 6 })
              
              Text('被邀请人可获得1斤蔬菜')
                .fontSize(14)
                .fontColor('#FF7043')
                .backgroundColor('#FFF3E0')
                .padding({ left: 12, right: 12, top: 6, bottom: 6 })
                .layoutWeight(1)
            }
            .width('100%')
          }
          .width('90%')
          .backgroundColor(Color.White)
          .borderRadius(8)
          .margin({ top: 20 })
          
          // 分隔线和标题
          Row() {
            Divider().width(80).height(1).color('#DDDDDD')
            Text('立即邀请')
              .fontSize(16)
              .fontColor('#333333')
              .margin({ left: 16, right: 16 })
            Divider().width(80).height(1).color('#DDDDDD')
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          .margin({ top: 20, bottom: 20 })
          
          // 分享方式
          Row({ space: 40 }) {
            Column({ space: 8 }) {
              Image($r('app.media.weixin'))
                .width(50)
                .height(50)
              Text('好友')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignItems(HorizontalAlign.Center)
            
            Column({ space: 8 }) {
              Image($r('app.media.pyq'))
                .width(50)
                .height(50)
              Text('朋友圈')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignItems(HorizontalAlign.Center)
            
            Column({ space: 8 }) {
              Image($r('app.media.baocun'))
                .width(50)
                .height(50)
              Text('保存')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignItems(HorizontalAlign.Center)
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          .margin({ bottom: 30 })
        }
        .width('90%')
        .backgroundColor(Color.White)
        .borderRadius(16)
        .position({ x: '5%', y: '10%' })
      }
    }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .width('100%')
    .height('100%')
  }
}
