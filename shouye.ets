// shouye.ets  动态首页
import { router } from '@kit.ArkUI';
import { http } from '@kit.NetworkKit';
import prompt from '@ohos.promptAction';

interface PostItem {
  id: number;
  username: string;
  avatar_url?: string;
  content: string;
  image_url?: string;
  created_at: string;
  likes_count: number;
  comments_count: number;
  shares_count: number;
}

@Entry
@Component
struct ShouYePage {
  @State posts: PostItem[] = [];
  @State isLoading: boolean = false;
  @State likedPosts: Set<number> = new Set(); // 记录已点赞的动态
  @StorageLink('shouldRefreshPosts') shouldRefreshPosts: boolean = false;

  async aboutToAppear() {
    await this.fetchPosts();
  }

  // 页面显示时刷新数据
  onPageShow() {
    // 检查是否需要刷新数据
    if (this.shouldRefreshPosts) {
      this.fetchPosts();
      AppStorage.setOrCreate('shouldRefreshPosts', false);
    }
  }

  // 从详情页返回时更新数据
  onPageHide() {
    // 页面隐藏时保存状态
  }

  private async fetchPosts(): Promise<void> {
    this.isLoading = true;
    try {
      const resp = await http.createHttp().request(
        'http://192.168.31.246:6767/posts',
        { method: http.RequestMethod.GET }
      );
      if (resp.responseCode === 200) {
        this.posts = JSON.parse(resp.result as string) as PostItem[];
      }
    } catch (error) {
      prompt.showToast({ message: '获取动态失败' });
      console.error('获取动态失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private async likePost(post: PostItem, index: number): Promise<void> {
    // 防止重复点赞
    if (this.likedPosts.has(post.id)) {
      prompt.showToast({ message: '已经点过赞了' });
      return;
    }

    try {
      const resp = await http.createHttp().request(
        `http://192.168.31.246:6767/posts/${post.id}/like`,
        { method: http.RequestMethod.POST }
      );
      if (resp.responseCode === 200) {
        // 更新本地数据
        this.posts[index].likes_count++;
        // 记录已点赞状态
        this.likedPosts.add(post.id);
        prompt.showToast({ message: '点赞成功' });
      }
    } catch (error) {
      prompt.showToast({ message: '点赞失败' });
      console.error('点赞失败:', error);
    }
  }

  private navigateToDetail(post: PostItem): void {
    router.pushUrl({
      url: 'pages/dongtai',
      params: {
        post: JSON.stringify(post),
        postIndex: this.posts.findIndex(p => p.id === post.id).toString()
      }
    }).then(() => {
      // 导航成功后的回调
    }).catch((error) => {
      console.error('导航失败:', error);
    });
  }

  private async refreshPosts(): Promise<void> {
    await this.fetchPosts();
  }

  @Builder
  PostItemBuilder(post: PostItem, index: number) {
    Column() {
      // 用户信息区域
      Row({ space: 12 }) {
        Image($r('app.media.touxiang'))
          .width(50)
          .height(50)
          .borderRadius(25)
          .backgroundColor('#f0f0f0')
          .border({ width: 2, color: '#99CC33' })

        Column({ space: 4 }) {
          Text(post.username)
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')

          Text(post.created_at)
            .fontSize(12)
            .fontColor('#999999')
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        // 更多按钮
        Image($r('app.media.gengduo'))
          .width(20)
          .height(20)
          .fillColor('#999999')
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 20 })

      // 动态内容
      Text(post.content)
        .fontSize(16)
        .fontColor('#333333')
        .lineHeight(24)
        .padding({ left: 20, right: 20, top: 16 })
        .width('100%')

      // 图片（如果有）
      if (post.image_url) {
        Image(post.image_url)
          .width('92%')
          .height(220)
          .borderRadius(16)
          .margin({ top: 16 })
          .backgroundColor('#f5f5f5')
          .objectFit(ImageFit.Cover)
      }

      // 互动区域
      Row() {
        // 点赞
        Row({ space: 8 }) {
          Image(this.likedPosts.has(post.id) ? $r('app.media.xiai') : $r('app.media.xiai'))
            .width(26)
            .height(26)
            .fillColor(this.likedPosts.has(post.id) ? '#ff4757' : '#666666')
            .onClick(() => this.likePost(post, index))
            .animation({
              duration: 200,
              curve: Curve.EaseInOut
            })

          Text(post.likes_count.toString())
            .fontSize(15)
            .fontColor('#666666')
            .fontWeight(FontWeight.Medium)
        }
        .padding({ left: 8, right: 8, top: 8, bottom: 8 })
        .borderRadius(20)
        .backgroundColor(this.likedPosts.has(post.id) ? '#fff5f5' : '#f8f9fa')

        // 评论
        Row({ space: 8 }) {
          Image($r('app.media.pinglun'))
            .width(26)
            .height(26)
            .fillColor('#666666')
            .onClick(() => this.navigateToDetail(post))

          Text(post.comments_count.toString())
            .fontSize(15)
            .fontColor('#666666')
            .fontWeight(FontWeight.Medium)
        }
        .padding({ left: 8, right: 8, top: 8, bottom: 8 })
        .borderRadius(20)
        .backgroundColor('#f8f9fa')

        // 分享
        Row({ space: 8 }) {
          Image($r('app.media.fenxiang'))
            .width(26)
            .height(26)
            .fillColor('#666666')

          Text(post.shares_count.toString())
            .fontSize(15)
            .fontColor('#666666')
            .fontWeight(FontWeight.Medium)
        }
        .padding({ left: 8, right: 8, top: 8, bottom: 8 })
        .borderRadius(20)
        .backgroundColor('#f8f9fa')
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceAround)
      .padding({ left: 20, right: 20, top: 20, bottom: 20 })
    }
    .width('100%')
    .backgroundColor(Color.White)
    .borderRadius(16)
    .margin({ left: 16, right: 16, bottom: 16 })
    .shadow({
      radius: 12,
      color: '#1a000000',
      offsetX: 0,
      offsetY: 4
    })
    .onClick(() => this.navigateToDetail(post))
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Column() {
          Text('动态广场')
            .fontSize(22)
            .fontWeight(FontWeight.Bold)
            .fontColor('#333333')

          Text('发现精彩内容')
            .fontSize(12)
            .fontColor('#999999')
            .margin({ top: 2 })
        }
        .alignItems(HorizontalAlign.Start)

        Blank()

        Row({ space: 16 }) {
          Image($r('app.media.sousuo'))
            .width(24)
            .height(24)
            .fillColor('#666666')

          Image($r('app.media.shuaxin'))
            .width(24)
            .height(24)
            .fillColor('#99CC33')
            .onClick(() => this.refreshPosts())
            .animation({
              duration: 300,
              curve: Curve.EaseInOut
            })
        }
      }
      .width('100%')
      .height(64)
      .padding({ left: 20, right: 20 })
      .backgroundColor(Color.White)
      .border({ width: { bottom: 1 }, color: '#f0f0f0' })

      // 动态列表
      if (this.isLoading && this.posts.length === 0) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#99CC33')
          
          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 12 })
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
      } else {
        Refresh({ refreshing: $$this.isLoading }) {
          List({ space: 0 }) {
            ForEach(this.posts, (post: PostItem, index: number) => {
              ListItem() {
                this.PostItemBuilder(post, index)
              }
            })
          }
          .width('100%')
          .backgroundColor('#f8f9fa')
          .padding({ top: 12 })
        }
        .onRefreshing(() => {
          this.refreshPosts();
        })
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f8f9fa')
  }
}
