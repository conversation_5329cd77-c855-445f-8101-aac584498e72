//模块导入和初始化
const express = require('express');
const bodyParser=require('body-parser')
const cors = require('cors');
const {initializeDatabase} = require('./config/db');
require('dotenv').config();
// 初始化Express应用
const app = express();
// 配置中间件
app.use(cors());
app.use(express.json());
//数据库初始化
initializeDatabase();
// 路由配置
const authRoutes=require('./routes/index')
app.use('/api/suixin', authRoutes);
// http://localhost:3000/api/suixin/login
// 启动服务器 
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});