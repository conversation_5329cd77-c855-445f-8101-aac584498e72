import {  router } from "@kit.ArkUI"

interface Address{
  id :string
  name: string
  address:string
  phone:string

}

@Entry
@Component
struct Myaddress {
  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;
  @State addresses: Address[] = [
    {
      id: '1',
      name: '蒋先生',
      phone: '180****0626',
      address: '河北省石家庄市高新区100号大厦100层'
    },
    {
      id: '2',
      name: '蒋先生',
      phone: '180****0626',
      address: '甘肃省兰州市高新区100号大厦100层'
    },
    {
      id: '3',
      name: '蒋先生',
      phone: '180****0626',
      address: '山西省太原市高新区100号大厦100层'
    },
    {
      id: '4',
      name: '蒋先生',
      phone: '180****0626',
      address: '内蒙古自治区高新区100号大厦100层'
    },
    {
      id: '5',
      name: '蒋先生',
      phone: '180****0626',
      address: '河南省郑州市高新区100号大厦100层'
    }
  ];

  build() {
    Column() {
      Row() {
        Image($r("app.media.return"))
          .width(20)
          .margin({ left: 10, top: 5 })
          .fillColor('#ffffff')
          .onClick(() => {
            router.back()
          })
        Text('我的地址')
          .fontColor('#ffffff')
          .margin({ left: 80, top: 5 })
          .fontSize(22)
          .fontWeight(88)
        Row({
          space: 10
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius: 20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({
          left: 20,
          right: 20,
          top: 8,
          bottom: 8
        })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({ left: 22, right: 10, top: 5 });
      }
      .justifyContent(FlexAlign.SpaceBetween)
      .width('100%')
      .padding({ left: 5, right: 5 })
      .height(60)
      .backgroundColor('#99CC33')
      // 动态渲染地址列表
      ForEach(this.addresses, (address: Address) => {
        Row({ space: 10 }) {
          // 默认第一个地址为选中状态
          if (address.id === '1') {
            Image($r('app.media.exitcircle'))
              .width(20)
              .height(20)
              .fillColor('#99CC33')
          } else {
            Image($r('app.media.unse'))
              .width(20)
              .height(20);
          }

          Column({ space:10}) {
            Text(address.address)
              .fontSize(14);
            Row({ space: 5 }) {
              Text(address.name)
                .fontSize(14);
              Text(address.phone)
                .fontSize(14)
            }
          }
          .alignItems(HorizontalAlign.Start)
          .flexGrow(1);

          Image($r('app.media.delete'))
            .position({x:310,y:-10})
            .padding(3)
            .width(20)
            .height(20)
            .backgroundColor('#FF0000')
            .fillColor('#ffffff')
            .borderRadius({topRight:5,bottomLeft:5})
            .onClick(() => {
              this.onDeleteAddress(address.id);
            });
        }
        .padding(10)
        .height(80)
        .width(340)
        .borderRadius(10)
        .backgroundColor('#fff')
        .margin(10)
      }, (address: Address) => address.id) // key 为 address.id

      // 添加新地址按钮
      Button('添加新地址')
        .onClick(() => {
          router.replaceUrl({url:'pages/NewAddress'})
        })
        .width(320)
        .height(40)
        .borderRadius(10)
        .backgroundColor('#99CC33')
        .margin({ top: 20 });
    }.padding({ top: px2vp(this.topRectHeight)+15, bottom: px2vp(this.bottomRectHeight)+40,left:0,right:0 })
    .width('100%')
    .height('100%')
    .backgroundColor('#F4F8FF')
  }

  onDeleteAddress(id: string) {
    console.log('删除地址：' + id);
    this.addresses = this.addresses.filter(item => item.id !== id);
  }

  onAddNewAddress() {
    console.log('添加新地址');
    // 这里可以跳转到添加地址页面或弹出表单
  }
}
