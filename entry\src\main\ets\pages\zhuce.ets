import { promptAction } from '@kit.ArkUI';
import { router } from '@kit.ArkUI';
import http from '@ohos.net.http';

interface RegisterRequest {
  account: string;
  nickName: string;
  gender: string;
  age: number;
  phoneNumber: string;
  password: string;
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

@Entry
@Component
struct Register {
  @State account: string = '';
  @State nickName: string = '';
  @State gender: string = '男';
  @State age: string = '';
  @State phoneNumber: string = '';
  @State password: string = '';
  @State confirmPassword: string = '';
  @State isLoading: boolean = false;

  // 焦点状态
  @State accountFocused: boolean = false;
  @State nickNameFocused: boolean = false;
  @State ageFocused: boolean = false;
  @State phoneFocused: boolean = false;
  @State passwordFocused: boolean = false;
  @State confirmPasswordFocused: boolean = false;

  private baseUrl: string = 'http://192.168.31.34:8080/api';

  build() {
    Column() {
      Stack() {
        Image($r('app.media.starter1'))
          .width('100%')
          .height('100%')

        Column({ space: 15 }) {
          Text('注册')
            .fontSize(35)
            .fontWeight(300)

          // 账户输入框
          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '账户', text: $$this.account })
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(50)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.accountFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.accountFocused = true;
                this.clearOtherFocus('account');
              })
              .onBlur(() => {
                this.accountFocused = false;
              });

            Image($r('app.media.xiaoxi'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          // 昵称输入框
          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '昵称', text: $$this.nickName })
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(50)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.nickNameFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.nickNameFocused = true;
                this.clearOtherFocus('nickName');
              })
              .onBlur(() => {
                this.nickNameFocused = false;
              });

            Image($r('app.media.xiaoxi'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          // 性别选择
          Row({ space: 10 }) {
            Text('性别:')
              .fontSize(16)
              .fontColor('#666666')

            Row({ space: 20 }) {
              Row({ space: 5 }) {
                Radio({ value: '男', group: 'gender' })
                  .checked(this.gender === '男')
                  .onChange((isChecked: boolean) => {
                    if (isChecked) {
                      this.gender = '男';
                    }
                  })
                Text('男')
                  .fontSize(14)
                  .fontColor('#666666')
              }

              Row({ space: 5 }) {
                Radio({ value: '女', group: 'gender' })
                  .checked(this.gender === '女')
                  .onChange((isChecked: boolean) => {
                    if (isChecked) {
                      this.gender = '女';
                    }
                  })
                Text('女')
                  .fontSize(14)
                  .fontColor('#666666')
              }
            }
          }
          .width('100%')
          .justifyContent(FlexAlign.Start)

          // 年龄输入框
          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '年龄', text: $$this.age })
              .type(InputType.Number)
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(50)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.ageFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.ageFocused = true;
                this.clearOtherFocus('age');
              })
              .onBlur(() => {
                this.ageFocused = false;
              });

            Image($r('app.media.xiaoxi'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          // 手机号输入框
          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '手机号码', text: $$this.phoneNumber })
              .type(InputType.PhoneNumber)
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(50)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.phoneFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.phoneFocused = true;
                this.clearOtherFocus('phone');
              })
              .onBlur(() => {
                this.phoneFocused = false;
              });

            Image($r('app.media.xiaoxi'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          // 密码输入框
          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '密码', text: $$this.password })
              .type(InputType.Password)
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(50)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.passwordFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.passwordFocused = true;
                this.clearOtherFocus('password');
              })
              .onBlur(() => {
                this.passwordFocused = false;
              });

            Image($r('app.media.u1915'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          // 确认密码输入框
          Stack({ alignContent: Alignment.Start }) {
            TextInput({ placeholder: '确认密码', text: $$this.confirmPassword })
              .type(InputType.Password)
              .placeholderColor('#CFD5D5')
              .padding({ left: 30, right: 10, top: 10, bottom: 10 })
              .height(50)
              .backgroundColor('#ffffff')
              .borderColor('#999999')
              .borderWidth(1)
              .border({
                width: 1,
                color: this.confirmPasswordFocused ? '#FF3E3E' : '#E5E5E5',
                radius: 12
              })
              .onFocus(() => {
                this.confirmPasswordFocused = true;
                this.clearOtherFocus('confirmPassword');
              })
              .onBlur(() => {
                this.confirmPasswordFocused = false;
              });

            Image($r('app.media.u1915'))
              .width(16)
              .height(16)
              .margin({ left: 10 })
          }

          Button(this.isLoading ? '注册中...' : '注册')
            .type(ButtonType.Normal)
            .backgroundColor('#F48C95')
            .width('100%')
            .fontColor('#ffffff')
            .fontSize(25)
            .fontWeight(600)
            .height(60)
            .borderRadius(18)
            .enabled(!this.isLoading)
            .onClick(() => {
              this.handleRegister();
            })

          Button('已有账号？去登录')
            .type(ButtonType.Normal)
            .backgroundColor('#ffffff')
            .border({ width: 1, color: '#F48C95' })
            .width('100%')
            .fontColor('#F48C95')
            .fontSize(18)
            .height(50)
            .borderRadius(18)
            .onClick(() => {
              router.back();
            })
        }
        .padding(20)
        .margin({ top: 350 })
        .width('100%')
        .height('100%')
        .borderRadius(50)
        .backgroundColor('#ffffff')
      }
    }
    .height('100%')
    .width('100%')
  }

  private clearOtherFocus(currentField: string) {
    if (currentField !== 'account') this.accountFocused = false;
    if (currentField !== 'nickName') this.nickNameFocused = false;
    if (currentField !== 'age') this.ageFocused = false;
    if (currentField !== 'phone') this.phoneFocused = false;
    if (currentField !== 'password') this.passwordFocused = false;
    if (currentField !== 'confirmPassword') this.confirmPasswordFocused = false;
  }

  private async handleRegister() {
    console.log('=== 开始注册流程 ===');

    // 表单验证
    if (!this.account.trim()) {
      promptAction.showToast({ message: "请输入账户" });
      return;
    }

    if (!this.nickName.trim()) {
      promptAction.showToast({ message: "请输入昵称" });
      return;
    }

    if (!this.age.trim()) {
      promptAction.showToast({ message: "请输入年龄" });
      return;
    }

    const ageNum = parseInt(this.age);
    if (isNaN(ageNum) || ageNum < 1 || ageNum > 150) {
      promptAction.showToast({ message: "请输入有效的年龄" });
      return;
    }

    if (!this.phoneNumber.trim()) {
      promptAction.showToast({ message: "请输入手机号码" });
      return;
    }

    // 简单的手机号验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.phoneNumber.trim())) {
      promptAction.showToast({ message: "请输入正确的手机号码" });
      return;
    }

    if (!this.password.trim()) {
      promptAction.showToast({ message: "请输入密码" });
      return;
    }

    if (this.password.length < 6) {
      promptAction.showToast({ message: "密码长度不能少于6位" });
      return;
    }

    if (!this.confirmPassword.trim()) {
      promptAction.showToast({ message: "请确认密码" });
      return;
    }

    if (this.password !== this.confirmPassword) {
      promptAction.showToast({ message: "两次输入的密码不一致" });
      return;
    }

    console.log('表单验证通过，开始注册请求');
    console.log('账户:', this.account);
    console.log('昵称:', this.nickName);
    console.log('性别:', this.gender);
    console.log('年龄:', ageNum);
    console.log('手机号:', this.phoneNumber);

    this.isLoading = true;

    try {
      const httpRequest = http.createHttp();
      const registerRequest: RegisterRequest = {
        account: this.account.trim(),
        nickName: this.nickName.trim(),
        gender: this.gender,
        age: ageNum,
        phoneNumber: this.phoneNumber.trim(),
        password: this.password.trim()
      };

      const requestUrl = `${this.baseUrl}/user/register`;
      console.log('请求URL:', requestUrl);
      console.log('请求数据:', JSON.stringify(registerRequest));

      const response = await httpRequest.request(requestUrl, {
        method: http.RequestMethod.POST,
        header: {
          'Content-Type': 'application/json'
        },
        extraData: JSON.stringify(registerRequest),
        connectTimeout: 10000,
        readTimeout: 10000
      });

      console.log('=== 收到响应 ===');
      console.log('HTTP状态码:', response.responseCode);
      console.log('响应数据:', response.result);

      if (response.responseCode === 200) {
        const result: ApiResponse<void> = JSON.parse(response.result as string);
        console.log('解析后的结果:', JSON.stringify(result));

        if (result.code === 200) {
          console.log('注册成功');
          promptAction.showToast({ message: "注册成功，请登录" });
          await this.navigateToLogin();
        } else {
          console.error('注册失败:', result.message);
          promptAction.showToast({
            message: result.message || "注册失败，请重试"
          });
        }
      } else {
        console.error('HTTP请求失败，状态码:', response.responseCode);
        promptAction.showToast({ message: "网络请求失败" });
      }

      httpRequest.destroy();
    } catch (error) {
      console.error('=== 注册异常 ===');
      console.error('异常信息:', JSON.stringify(error));
      promptAction.showToast({ message: "网络异常，请稍后重试" });
    } finally {
      this.isLoading = false;
      console.log('=== 注册流程结束 ===');
    }
  }

  private async navigateToLogin(): Promise<void> {
    try {
      await router.back();
      console.log('返回登录页面成功');
    } catch (error) {
      console.error('页面跳转失败:', error);
      promptAction.showToast({ message: "页面跳转失败" });
    }
  }
}