const jwt = require(`jsonwebtoken`);
require(`dotenv`).config();

const authenticate = (req, res, next) => {
    const token = req.header(`Authorization`)?.replace('Bearer', '');

    if (!token) {
        return res.status(401).json({ 
            messag: "No token,authorization denied(未提供令牌，授权被拒绝)" })
    }

    try{
        const decoded=jwt.verify(token,process.env.JWT_SECRET);
        req.user=decoded;
        next();
    }catch(error){
        res.status(401).json({messag:": 'Token is not valid(令牌无效)"})
    }
};

module.exports={authenticate}