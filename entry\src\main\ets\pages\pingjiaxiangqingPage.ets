import router from '@ohos.router';

@Entry
@Component
struct EvaluationDetail {
  // 轮播图控制器
  private swiperController: SwiperController = new SwiperController()
  @State commentText: string = ''

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Image($r('app.media.fanhui1')) // 返回图标
          .width(24)
          .height(24)
          .margin({ left: 16 })
          .onClick(() => {
            router.back()
          })

        Text('评价详情')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .fontColor(Color.White)
          .margin({left:40})

        Row({
          space: 10,
        }) {
          Image($r('app.media.pot'))
            .width(24)
            .fillColor('#ffffff');

          Divider()
            .vertical(true)
            .strokeWidth(1)
            .color('#ffffff')
            .height(22)

          Image($r('app.media.exitcircle'))
            .width(24)
            .fillColor('#ffffff');
        }
        .border({
          width: 1,
          radius:20,
          color: '#6B8F24' // 淡黑色边框
        })
        .padding({ left: 20, right: 20,top: 8,bottom:8 })
        .backgroundColor('#6B8F24') // 淡黑色背景
        .margin({left:40}); // 调整右边距和顶边距
      }
      .width('100%')
      .height(56)
      .backgroundColor('#8BC34A')

      // 内容区域 - 可滑动
      Scroll() {
        Column({ space: 0 }) {
          // 商品信息部分
          Row({ space: 12 }) {
            Image($r('app.media.apple3')) // 商品图片
              .width(80)
              .height(80)
              .borderRadius(8)
              .objectFit(ImageFit.Cover)
              .margin({ left: 16 })

            Column({ space: 4 }) {
              Text('纯天然大苹果来自大自然的天然产物')
                .fontSize(16)
                .fontColor('#333333')
                .maxLines(2)
                .textOverflow({ overflow: TextOverflow.Ellipsis })

              Row() {
                Text('¥ 60.00')
                  .fontSize(16)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Bold)
                
                Text('×3')
                  .fontSize(14)
                  .fontColor('#999999')
                  .margin({ left: 170 })
              }
              .width('100%')
              .margin({ top: 8 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start)
            .margin({ right: 16 })
          }
          .width('100%')
          .backgroundColor(Color.White)
          .padding({ top: 16, bottom: 16 })

          // 用户评价部分
          Column({ space: 12 }) {
            // 用户信息
            Row() {
              Image($r('app.media.a')) // 用户头像
                .width(40)
                .height(40)
                .borderRadius(20)
                .margin({ left: 16 })

              Text('小**菜')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .margin({ left: 12 })
                .layoutWeight(1)

              Text('2024.12.12')
                .fontSize(14)
                .fontColor('#999999')
                .margin({ right: 16 })
            }
            .width('100%')
            .padding({ top: 16 })

            // 评价内容
            Text('水果确实很新鲜，验证了，确实是有机的，快递速度非常快，非常快，非常快的，非常快，非常快，非常快。')
              .fontSize(16)
              .fontColor('#333333')
              .lineHeight(24)
              .margin({ left: 16, right: 16, top: 8 })

            // 评价图片
            Column({ space: 8 }) {
              Image($r('app.media.apple2')) // 苹果切开图
                .width('90%')
                .aspectRatio(2)
                .objectFit(ImageFit.Cover)
                .borderRadius(8)

              Image($r('app.media.apple4')) // 多个苹果图
                .width('90%')
                .aspectRatio(2)
                .objectFit(ImageFit.Cover)
                .borderRadius(8)
            }
            .width('100%')
            .alignItems(HorizontalAlign.Center)
            .margin({ bottom: 16 })
          }
          .width('100%')
          .backgroundColor(Color.White)
          .margin({ top: 8 })

          // 回复标题
          Text('2条回复')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .margin({ left: 16, top: 16, bottom: 12 })
            .width('100%')

          // 回复列表
          Column({ space: 0 }) {
            // 回复项 1
            Column({ space: 8 }) {
              Row({ space: 12 }) {
                Image($r('app.media.a')) // 用户头像
                  .width(40)
                  .height(40)
                  .borderRadius(20)
                  .margin({ left: 16 })

                Column({ space: 4 }) {
                  Text('小**菜')
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                  
                  Row() {
                    Text('2024.12.13')
                      .fontSize(14)
                      .fontColor('#999999')
                  }
                }
                .alignItems(HorizontalAlign.Start)
                .layoutWeight(1)
              }
              .width('100%')

              Text('评价的很好，特别赞成！')
                .fontSize(16)
                .fontColor('#333333')
                .margin({ left: 68, right: 16 })
                .margin({ top: 4, bottom: 16 })
            }
            .width('100%')
            .backgroundColor(Color.White)

            Divider()
              .strokeWidth(1)
              .color('#f0f0f0')
              .margin({ left: 16, right: 16 })

            // 回复项 2
            Column({ space: 8 }) {
              Row({ space: 12 }) {
                Image($r('app.media.a')) // 用户头像
                  .width(40)
                  .height(40)
                  .borderRadius(20)
                  .margin({ left: 16 })

                Column({ space: 4 }) {
                  Text('小**瓜')
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                  
                  Row() {
                    Text('2024.12.12')
                      .fontSize(14)
                      .fontColor('#999999')
                  }
                }
                .alignItems(HorizontalAlign.Start)
                .layoutWeight(1)
              }
              .width('100%')

              Text('评价的很好，特别赞成！')
                .fontSize(16)
                .fontColor('#333333')
                .margin({ left: 68, right: 16 })
                .margin({ top: 4, bottom: 16 })
            }
            .width('100%')
            .backgroundColor(Color.White)
          }
          .backgroundColor(Color.White)
          .margin({ bottom: 80 }) // 为底部输入框留出空间
        }
      }
      .layoutWeight(1)
      .backgroundColor('#f5f5f5')

      // 固定在底部的输入框
      Row({ space: 12 }) {
        TextInput({ 
          placeholder: '说点什么呗~',
          text: this.commentText
        })
          .fontSize(16)
          .fontColor('#333333')
          .layoutWeight(1)
          .padding(12)
          .backgroundColor('#f5f5f5')
          .borderRadius(20)
          .height(40)
          .onChange((value: string) => {
            this.commentText = value
          })

        Image($r('app.media.xiaoxi')) // 发送图标
          .width(30)
          .height(30)
          //.backgroundColor('#8BC34A')
          .borderRadius(20)
          //.padding(8)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 10, bottom: 10 })
      .backgroundColor(Color.White)
      .border({ width: 1, color: '#eeeeee', style: BorderStyle.Solid })
      .position({ x: 0, y: '100%' })
      .translate({ y: -60 }) // 确保固定在底部
    }
    .height('100%')
    .width('100%')
    .backgroundColor('#f5f5f5')
  }
}
